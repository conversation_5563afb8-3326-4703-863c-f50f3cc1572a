# Website DDoS防护系统

## 概述

本系统为website模块提供了完整的DDoS防护解决方案，包括多层次的限流、黑名单管理、访问统计等功能。

## 核心组件

### 1. 限流注解 (@RateLimit)

用于方法级别的限流控制：

```java
@RateLimit(
    key = "homepage",                    // 限流key
    count = 60,                         // 时间窗口内最大请求数
    time = 1,                           // 时间窗口大小
    timeUnit = TimeUnit.MINUTES,        // 时间单位
    limitType = LimitType.IP,           // 限流类型
    message = "访问过于频繁，请稍后再试"    // 限流提示信息
)
```

**限流类型说明：**
- `IP`: 按IP地址限流
- `USER`: 按用户限流
- `GLOBAL`: 全局限流
- `CUSTOM`: 自定义限流

### 2. DDoS防护服务 (DDoSProtectionService)

提供核心的防护功能：

```java
// 检查IP是否被封禁
boolean isIpBanned(String ip)

// 记录IP访问统计
void recordIpAccess(String ip)

// 临时封禁IP
void banIpTemporarily(String ip, int durationMinutes)

// 添加/移除黑名单
void addToBlacklist(String ip, String reason)
void removeFromBlacklist(String ip)

// 获取访问统计
long getIpAccessCount(String ip, int minutes)
long getGlobalAccessCount(int minutes)
```

### 3. 限流服务 (RateLimitService)

基于Redis的高性能限流实现：

```java
// 检查是否允许访问
boolean isAllowed(RateLimit rateLimit, HttpServletRequest request, String methodSignature)

// 获取当前限流计数
long getCurrentCount(String key)

// 清除限流记录
void clearRateLimit(String key)
```

### 4. 防护拦截器 (DDoSProtectionInterceptor)

全局拦截器，对所有website公开接口进行防护：
- IP黑名单检查
- 全局限流检查
- IP限流检查
- 访问统计记录

## 配置说明

### 基础配置 (application-ddos.yml)

```yaml
website:
  ddos-protection:
    enabled: true  # 是否启用防护
    
    global:
      max-requests-per-second: 1000    # 全局每秒最大请求数
      max-requests-per-minute: 10000   # 全局每分钟最大请求数
    
    ip:
      max-requests-per-second: 10      # 单IP每秒最大请求数
      max-requests-per-minute: 300     # 单IP每分钟最大请求数
      max-requests-per-hour: 3600      # 单IP每小时最大请求数
      max-requests-per-day: 50000      # 单IP每天最大请求数
      ban-threshold-per-minute: 500    # 触发临时封禁的阈值
      ban-duration-minutes: 30         # 临时封禁时长
    
    black-list:
      enabled: true                    # 是否启用黑名单
      check-interval-seconds: 60       # 黑名单检查间隔
      auto-add-threshold: 1000         # 自动加入黑名单的阈值
      expire-hours: 24                 # 黑名单过期时间
```

### 环境配置

- **开发环境**: 默认关闭防护
- **测试环境**: 放宽限制便于测试
- **生产环境**: 严格限制确保安全

## 使用方法

### 1. 接口级限流

在Controller方法上添加@RateLimit注解：

```java
@GetMapping("/homepage")
@RateLimit(key = "homepage", count = 60, time = 1, timeUnit = TimeUnit.MINUTES)
public RestResponse<WebsiteHomepageVo> getHomepage(HttpServletRequest request) {
    // 业务逻辑
}
```

### 2. 管理接口

系统提供了完整的管理接口：

```bash
# 获取IP统计
GET /api/website-ddos/stats/ip/{ip}

# 获取全局统计
GET /api/website-ddos/stats/global

# 添加黑名单
POST /api/website-ddos/blacklist/add?ip=***********&reason=恶意攻击

# 移除黑名单
POST /api/website-ddos/blacklist/remove?ip=***********

# 临时封禁
POST /api/website-ddos/ban/temporary?ip=***********&durationMinutes=60

# 清除限流记录
POST /api/website-ddos/rate-limit/clear?key=homepage:ip:***********
```

### 3. 监控和告警

系统自动记录以下日志：
- IP访问统计
- 限流触发记录
- 黑名单操作记录
- 异常访问告警

## 防护策略

### 多层防护

1. **全局拦截器**: 最外层防护，检查黑名单和基础限流
2. **接口限流**: 针对具体接口的精细化限流
3. **缓存保护**: 利用Spring Cache减少数据库压力
4. **自动封禁**: 超过阈值自动临时封禁

### 渐进式防护

1. **正常访问**: 无限制
2. **频繁访问**: 触发限流，返回429状态码
3. **异常访问**: 临时封禁30分钟
4. **恶意攻击**: 加入黑名单24小时

### 智能识别

- **真实IP获取**: 支持代理和负载均衡环境
- **访问模式分析**: 识别正常用户和攻击行为
- **动态阈值调整**: 根据访问模式自动调整防护策略

## 性能优化

### Redis优化

- 使用Lua脚本保证原子性
- 合理设置过期时间避免内存泄漏
- 定时清理过期数据

### 缓存策略

- 限流状态缓存
- 黑名单缓存
- 访问统计缓存

### 异步处理

- 访问统计异步记录
- 黑名单检查异步更新
- 日志记录异步处理

## 故障处理

### 异常降级

当Redis不可用时：
- 限流检查返回true（允许通过）
- 黑名单检查返回false（不封禁）
- 记录错误日志但不影响业务

### 监控指标

- 限流触发次数
- 黑名单命中次数
- 系统响应时间
- Redis连接状态

## 最佳实践

1. **合理设置阈值**: 根据业务特点调整限流参数
2. **监控告警**: 设置关键指标的监控告警
3. **定期清理**: 定期清理过期的防护数据
4. **白名单机制**: 为可信IP设置白名单
5. **日志分析**: 定期分析访问日志优化防护策略

## 注意事项

1. 开发环境默认关闭防护，避免影响开发调试
2. 生产环境需要根据实际情况调整配置参数
3. 需要确保Redis服务稳定运行
4. 建议配合CDN、WAF等其他防护手段使用
