package com.hightop.benyin.website.infrastructure.security;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * DDoS防护服务
 * <AUTHOR>
 * @date 2025-01-21
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
@Slf4j
public class DDoSProtectionService {
    
    RedisTemplate<String, Object> redisTemplate;
    DDoSProtectionConfig config;
    
    private static final String BLACKLIST_PREFIX = "website:blacklist:";
    private static final String IP_STATS_PREFIX = "website:ip_stats:";
    private static final String GLOBAL_STATS_PREFIX = "website:global_stats:";
    private static final String BAN_PREFIX = "website:ban:";
    
    /**
     * 检查IP是否被封禁
     */
    public boolean isIpBanned(String ip) {
        return getIpBanMessage(ip) != null;
    }

    /**
     * 获取IP封禁消息
     */
    public String getIpBanMessage(String ip) {
        if (!config.isEnabled() || !config.getBlackList().isEnabled()) {
            return null;
        }

        try {
            // 检查黑名单
            Boolean inBlacklist = redisTemplate.hasKey(BLACKLIST_PREFIX + ip);
            if (Boolean.TRUE.equals(inBlacklist)) {
                log.warn("IP在黑名单中: {}", ip);
                return "您的IP已被加入黑名单，请联系管理员";
            }

            // 检查临时封禁
            String banKey = BAN_PREFIX + ip;
            Boolean isBanned = redisTemplate.hasKey(banKey);
            if (Boolean.TRUE.equals(isBanned)) {
                // 获取剩余封禁时间
                Long ttl = redisTemplate.getExpire(banKey, TimeUnit.SECONDS);
                if (ttl != null && ttl > 0) {
                    String timeInfo = formatTime(ttl);
                    log.warn("IP被临时封禁: {}, 剩余时间: {}秒", ip, ttl);
                    return String.format("您的IP因访问过于频繁被临时封禁，请%s后再试", timeInfo);
                } else {
                    return "您的IP因访问过于频繁被临时封禁，请稍后再试";
                }
            }

            return null;
        } catch (Exception e) {
            log.error("检查IP封禁状态异常: ip={}", ip, e);
            return null;
        }
    }

    /**
     * 格式化时间
     */
    private String formatTime(long seconds) {
        if (seconds <= 0) {
            return "稍后";
        }

        if (seconds < 60) {
            return seconds + "秒";
        } else if (seconds < 3600) {
            long minutes = seconds / 60;
            long remainingSeconds = seconds % 60;
            if (remainingSeconds == 0) {
                return minutes + "分钟";
            } else {
                return minutes + "分" + remainingSeconds + "秒";
            }
        } else {
            long hours = seconds / 3600;
            long remainingMinutes = (seconds % 3600) / 60;
            if (remainingMinutes == 0) {
                return hours + "小时";
            } else {
                return hours + "小时" + remainingMinutes + "分钟";
            }
        }
    }
    
    /**
     * 记录IP访问统计
     */
    public void recordIpAccess(String ip) {
        if (!config.isEnabled()) {
            return;
        }
        
        try {
            String now = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd-HH-mm"));
            String minuteKey = IP_STATS_PREFIX + ip + ":" + now;
            
            // 记录分钟级统计
            Long count = redisTemplate.opsForValue().increment(minuteKey);
            redisTemplate.expire(minuteKey, 2, TimeUnit.MINUTES);
            
            // 检查是否需要临时封禁
            if (count != null && count > config.getIp().getBanThresholdPerMinute()) {
                banIpTemporarily(ip, config.getIp().getBanDurationMinutes());
            }
            
            // 记录全局统计
            String globalKey = GLOBAL_STATS_PREFIX + now;
            redisTemplate.opsForValue().increment(globalKey);
            redisTemplate.expire(globalKey, 2, TimeUnit.MINUTES);
            
        } catch (Exception e) {
            log.error("记录IP访问统计异常: ip={}", ip, e);
        }
    }
    
    /**
     * 临时封禁IP
     */
    public void banIpTemporarily(String ip, int durationMinutes) {
        try {
            String banKey = BAN_PREFIX + ip;
            redisTemplate.opsForValue().set(banKey, LocalDateTime.now().toString(), durationMinutes, TimeUnit.MINUTES);
            log.warn("IP被临时封禁: ip={}, duration={}分钟", ip, durationMinutes);
        } catch (Exception e) {
            log.error("临时封禁IP异常: ip={}", ip, e);
        }
    }
    
    /**
     * 添加IP到黑名单
     */
    public void addToBlacklist(String ip, String reason) {
        if (!config.getBlackList().isEnabled()) {
            return;
        }
        
        try {
            String blacklistKey = BLACKLIST_PREFIX + ip;
            String value = String.format("{\"reason\":\"%s\",\"addTime\":\"%s\"}", 
                reason, LocalDateTime.now().toString());
            
            redisTemplate.opsForValue().set(blacklistKey, value, 
                config.getBlackList().getExpireHours(), TimeUnit.HOURS);
            
            log.warn("IP已添加到黑名单: ip={}, reason={}", ip, reason);
        } catch (Exception e) {
            log.error("添加IP到黑名单异常: ip={}, reason={}", ip, reason, e);
        }
    }
    
    /**
     * 从黑名单移除IP
     */
    public void removeFromBlacklist(String ip) {
        try {
            redisTemplate.delete(BLACKLIST_PREFIX + ip);
            redisTemplate.delete(BAN_PREFIX + ip);
            log.info("IP已从黑名单移除: {}", ip);
        } catch (Exception e) {
            log.error("从黑名单移除IP异常: ip={}", ip, e);
        }
    }

    /**
     * 解除IP临时封禁
     */
    public void removeBan(String ip) {
        try {
            String banKey = BAN_PREFIX + ip;
            redisTemplate.delete(banKey);
            log.info("IP临时封禁已解除: ip={}", ip);
        } catch (Exception e) {
            log.error("解除IP临时封禁异常: ip={}", ip, e);
        }
    }
    
    /**
     * 获取IP访问统计
     */
    public long getIpAccessCount(String ip, int minutes) {
        try {
            long totalCount = 0;
            LocalDateTime now = LocalDateTime.now();
            
            for (int i = 0; i < minutes; i++) {
                String time = now.minusMinutes(i).format(DateTimeFormatter.ofPattern("yyyy-MM-dd-HH-mm"));
                String key = IP_STATS_PREFIX + ip + ":" + time;
                Object count = redisTemplate.opsForValue().get(key);
                if (count != null) {
                    totalCount += Long.parseLong(count.toString());
                }
            }
            
            return totalCount;
        } catch (Exception e) {
            log.error("获取IP访问统计异常: ip={}", ip, e);
            return 0;
        }
    }
    
    /**
     * 获取全局访问统计
     */
    public long getGlobalAccessCount(int minutes) {
        try {
            long totalCount = 0;
            LocalDateTime now = LocalDateTime.now();
            
            for (int i = 0; i < minutes; i++) {
                String time = now.minusMinutes(i).format(DateTimeFormatter.ofPattern("yyyy-MM-dd-HH-mm"));
                String key = GLOBAL_STATS_PREFIX + time;
                Object count = redisTemplate.opsForValue().get(key);
                if (count != null) {
                    totalCount += Long.parseLong(count.toString());
                }
            }
            
            return totalCount;
        } catch (Exception e) {
            log.error("获取全局访问统计异常", e);
            return 0;
        }
    }
    
    /**
     * 定时清理过期数据
     */
    @Scheduled(fixedRate = 300000) // 每5分钟执行一次
    public void cleanupExpiredData() {
        if (!config.isEnabled()) {
            return;
        }
        
        try {
            // 清理过期的IP统计数据
            String pattern = IP_STATS_PREFIX + "*";
            Set<String> keys = redisTemplate.keys(pattern);
            if (keys != null && !keys.isEmpty()) {
                for (String key : keys) {
                    Long ttl = redisTemplate.getExpire(key);
                    if (ttl != null && ttl <= 0) {
                        redisTemplate.delete(key);
                    }
                }
            }
            
            log.debug("DDoS防护数据清理完成");
        } catch (Exception e) {
            log.error("清理过期数据异常", e);
        }
    }
    
    /**
     * 获取客户端真实IP地址
     */
    public String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
