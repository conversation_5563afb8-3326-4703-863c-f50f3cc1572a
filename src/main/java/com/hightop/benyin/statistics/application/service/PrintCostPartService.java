package com.hightop.benyin.statistics.application.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.toolkit.MPJWrappers;
import com.google.common.collect.Lists;
import com.hightop.benyin.customer.domain.service.CustomerDeviceGroupDomainService;
import com.hightop.benyin.customer.infrastructure.entity.Customer;
import com.hightop.benyin.customer.infrastructure.entity.CustomerDeviceGroup;
import com.hightop.benyin.iot.infrastructure.entity.IotPrintCount;
import com.hightop.benyin.statistics.api.dto.CostPartQuery;
import com.hightop.benyin.statistics.api.dto.RepairCountQuery;
import com.hightop.benyin.statistics.domain.service.PrintCostPartServiceDomain;
import com.hightop.benyin.statistics.domain.service.ReplacePartCountServiceDomain;
import com.hightop.benyin.statistics.infrastructure.constants.StatisticsConstants;
import com.hightop.benyin.statistics.infrastructure.entity.*;
import com.hightop.benyin.statistics.infrastructure.entity.PrintCostPart;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.common.core.util.ExecutorUtils;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.fario.common.mybatis.PageQuery;
import com.hightop.fario.common.mybatis.util.PageHelper;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 零件打印成本统计
 *
 * <AUTHOR>
 * @date 2024/06/12 17:31
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
public class PrintCostPartService {

    PrintCostPartServiceDomain printCostPartServiceDomain;
    CustomerDeviceGroupDomainService customerDeviceGroupDomainService;
    ReplacePartCountServiceDomain replacePartCountServiceDomain;

    /**
     * 分页查询
     *
     * @param pageQuery {@link PageQuery}
     * @return {@link DataGrid}
     */
    public DataGrid<PrintCostPart> page(RepairCountQuery pageQuery) {
        return PageHelper.startPage(pageQuery, p ->
                this.printCostPartServiceDomain.selectJoinList(PrintCostPart.class,
                        MPJWrappers.lambdaJoin()
                                .selectAll(PrintCostPart.class)
                                .selectAs(Customer::getSeqId, PrintCostPart::getCustomerSeq)
                                .selectAs(Customer::getName, PrintCostPart::getCustomerName)
                                .selectAs(Customer::getSubbranch, PrintCostPart::getSubbranch)
                                .leftJoin(Customer.class, Customer::getId, PrintCostPart::getCustomerId)
                                .like(StringUtils.isNotBlank(pageQuery.getCustomerName()), Customer::getName, pageQuery.getCustomerName())
                                .eq(StringUtils.isNotBlank(pageQuery.getRegCliState()), PrintCostPart::getRegCliState, pageQuery.getRegCliState())
                                .in(CollectionUtils.isNotEmpty(pageQuery.getSerTypes()), RepairCount::getSerType, pageQuery.getSerTypes())
                                .in(CollectionUtils.isNotEmpty(pageQuery.getProductIds()), CustomerDeviceGroup::getProductId, pageQuery.getProductIds())
                                .like(StringUtils.isNotBlank(pageQuery.getCustomerSeq()), Customer::getSeqId, pageQuery.getCustomerSeq())
                                .between(pageQuery.getReportTimeStart() != null && pageQuery.getReportTimeEnd() != null, PrintCostPart::getCreatedAt, pageQuery.getReportTimeStart(), pageQuery.getReportTimeEnd())
                                .orderByDesc(PrintCostPart::getCreatedAt))
        );
    }

    public PrintCostPart getById(Long id) {
        return this.printCostPartServiceDomain.selectJoinOne(PrintCostPart.class,
                MPJWrappers.lambdaJoin()
                        .selectAll(PrintCostPart.class)
                        .selectAs(Customer::getSeqId, PrintCostPart::getCustomerSeq)
                        .selectAs(Customer::getName, PrintCostPart::getCustomerName)
                        .selectAs(Customer::getSubbranch, PrintCostPart::getSubbranch)
                        .leftJoin(Customer.class, Customer::getId, PrintCostPart::getCustomerId)
                        .eq(PrintCostPart::getId,id)
        );
    }

    /**
     * 全量耗材成本统计
     *
     * @param costPartQuery
     */
    public void initPrintCostPart(CostPartQuery costPartQuery) {
        //删除历史数据
        printCostPartServiceDomain.remove(Wrappers.<PrintCostPart>lambdaQuery()
                .ge(PrintCostPart::getId, 0)
                .eq(costPartQuery.getCustomerId() != null, PrintCostPart::getCustomerId, costPartQuery.getCustomerId())
                .eq(costPartQuery.getDeviceGroupId() != null, PrintCostPart::getDeviceGroupId, costPartQuery.getDeviceGroupId())
                .eq(costPartQuery.getProductId() != null, PrintCostPart::getProductId, costPartQuery.getDeviceGroupId())
        );

        //查询相关设备组
        List<CustomerDeviceGroup> customerDeviceGroups = this.customerDeviceGroupDomainService.lambdaQuery()
                .eq(costPartQuery.getCustomerId() != null, CustomerDeviceGroup::getCustomerId, costPartQuery.getCustomerId())
                .eq(costPartQuery.getProductId() != null, CustomerDeviceGroup::getProductId, costPartQuery.getProductId())
                .eq(costPartQuery.getDeviceGroupId() != null, CustomerDeviceGroup::getId, costPartQuery.getDeviceGroupId()).list();

        Map<Long, List<CustomerDeviceGroup>> customerDeviceGroupMap = customerDeviceGroups.stream().collect(Collectors.groupingBy(CustomerDeviceGroup::getProductId));

        ExecutorUtils.run(StatisticsConstants.EXECUTOR, () -> {
            for (Map.Entry<Long, List<CustomerDeviceGroup>> entry : customerDeviceGroupMap.entrySet()) {
                List<CustomerDeviceGroup> deviceGroups = entry.getValue();
                List<PrintCostPart> printCostParts = printCostPartServiceDomain.getBasePrintCostPart(entry.getKey());
                List<PrintCostPart> savePrintCostParts = Lists.newArrayList();
                for (CustomerDeviceGroup deviceGroup : deviceGroups) {
                    printCostParts.forEach(printCostPart -> {
                        printCostPart.setDeviceGroupId(deviceGroup.getId());
                        printCostPart.setCustomerId(deviceGroup.getCustomerId());
                        printCostPart.setDeviceGroup(deviceGroup.getDeviceGroup());
                        printCostPart.setRegCliState(deviceGroup.getRegCliState());
                        BigDecimal salePrice = new BigDecimal(printCostPart.getSaleUnitPrice());
                        BigDecimal cost = salePrice.divide(new BigDecimal(printCostPart.getLifespan()), 4, BigDecimal.ROUND_HALF_UP);
                        printCostPart.setOriCost(cost);

                        //查询零件更换记录
                        List<ReplacePartCount> replacePartCounts = replacePartCountServiceDomain.lambdaQuery()
                                .eq(ReplacePartCount::getCustomerId, deviceGroup.getCustomerId())
                                .eq(ReplacePartCount::getDeviceGroupId, deviceGroup.getId())
                                .eq(ReplacePartCount::getPartId, printCostPart.getPartId())
                                .list();
                        if (replacePartCounts.size() > 0) {
                            Long replaceNum = replacePartCounts.stream().mapToLong(ReplacePartCount::getNum).sum();
                            Integer totalCount = replacePartCounts.stream().mapToInt(ReplacePartCount::getTotalCount).sum();
                            Integer blackCount = replacePartCounts.stream().mapToInt(ReplacePartCount::getBlackWhiteCount).sum();
                            Integer colorCount = replacePartCounts.stream().mapToInt(ReplacePartCount::getColorCount).sum();
                            printCostPart.setReplaceCount(replaceNum.intValue());
                            printCostPart.setTotalCount(totalCount);
                            printCostPart.setBlackWhiteCount(blackCount);
                            printCostPart.setColorCount(colorCount);
                            //计算零件更换总金额
                            Long totalAmount = replacePartCounts.stream().map(v -> {
                                return v.getNum() * v.getSaleUnitPrice();
                            }).reduce(0L, Long::sum);
                            BigDecimal actualCost = new BigDecimal(totalAmount).divide(new BigDecimal(totalCount), 4, BigDecimal.ROUND_HALF_UP);
                            printCostPart.setActualCost(actualCost);
                        }
                        savePrintCostParts.add(printCostPart);
                    });
                }
            }
        });

    }
}
