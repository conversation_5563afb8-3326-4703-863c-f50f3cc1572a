package com.hightop.benyin.item.application.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hightop.benyin.item.api.query.ItemCategoryQuery;
import com.hightop.benyin.item.domain.service.ItemCategoryDomainService;
import com.hightop.benyin.item.domain.service.ItemCategoryTagsDomainService;
import com.hightop.benyin.item.domain.service.ItemServiceDomain;
import com.hightop.benyin.item.domain.service.SaleSkuServiceDomain;
import com.hightop.benyin.item.infrastructure.entity.Item;
import com.hightop.benyin.item.infrastructure.entity.ItemCategory;
import com.hightop.benyin.item.infrastructure.entity.ItemCategoryTags;
import com.hightop.benyin.item.infrastructure.entity.SaleSku;
import com.hightop.benyin.storage.domain.service.StorageArticleServiceDomain;
import com.hightop.benyin.storage.domain.service.StorageInventoryServiceDomain;
import com.hightop.benyin.storage.infrastructure.entity.StorageArticle;
import com.hightop.benyin.storage.infrastructure.entity.StorageInventory;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.fario.common.mybatis.util.PageHelper;
import com.hightop.magina.core.exception.MaginaException;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;


import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 商品管理-分类管理服务
 *
 * <AUTHOR>
 * @date 2023-10-31 11:27:11
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
public class ItemCategoryService {

    ItemCategoryDomainService itemCategoryDomainService;

    ItemCategoryTagsDomainService itemCategoryTagsDomainService;
    
    ItemServiceDomain itemServiceDomain;
    
    StorageInventoryServiceDomain inventoryServiceDomain;
    
    StorageArticleServiceDomain storageArticleServiceDomain;
    
    SaleSkuServiceDomain saleSkuServiceDomain;

    /**
     * 分页查询
     *
     * @param pageQuery
     * @return
     */
    public DataGrid<ItemCategory> list(ItemCategoryQuery pageQuery) {
        return PageHelper.startPage(pageQuery, p ->
            itemCategoryDomainService.lambdaQuery()
                .like(StringUtils.isNotBlank(pageQuery.getName()), ItemCategory::getName, pageQuery.getName())
                .like(StringUtils.isNotBlank(pageQuery.getCode()), ItemCategory::getCode, pageQuery.getCode())
                .eq(pageQuery.getStatus() != null, ItemCategory::getStatus, pageQuery.getStatus())
                .orderByAsc(ItemCategory::getSort)
                .list()
        );
    }

    /**
     * 商品管理-分类管理分页查询
     *
     * @return {@link DataGrid}
     */
    public ItemCategory getOne(Long id) {
        ItemCategory itemCategory = this.itemCategoryDomainService.getById(id);
        //查询标签
        Optional.ofNullable(itemCategory)
            .ifPresent(i -> i.setTagList(
                this.itemCategoryTagsDomainService.lambdaQuery()
                    .eq(ItemCategoryTags::getClassifyId, id).list()
            ));
        

        
        return itemCategory;
    }
    


    /**
     * 商品管理-分类管理添加
     *
     * @param itemCategory {@link ItemCategory}
     * @return true/false
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean save(ItemCategory itemCategory) {
        //名称校验
        checkNameRepeat(itemCategory);
        // 默认为一级
        itemCategory.setParentId(ItemCategory.TOP);
        // 设置默认状态为启用
        if (itemCategory.getStatus() == null) {
            itemCategory.setStatus(true); // 1表示启用
        }
        this.itemCategoryDomainService.save(itemCategory);
        // 同一设置分类id
        Optional.ofNullable(itemCategory.getTagList())
            .ifPresent(i -> {
                i.stream().forEach(e -> e.setClassifyId(itemCategory.getId()));
                this.itemCategoryTagsDomainService.saveBatch(i);
            });
        return true;
    }

    /**
     * 分类名称、编码、其下属性名称重复校验
     *
     * @param itemCategory
     * @return:
     * @Author: xhg
     * @Date: 2023/11/22 11:17
     */
    protected void checkNameRepeat(ItemCategory itemCategory) {
        Long count =
            this.itemCategoryDomainService.lambdaQuery()
                .eq(ItemCategory::getName, itemCategory.getName())
                .ne(Objects.nonNull(itemCategory.getId()), ItemCategory::getId, itemCategory.getId())
                .count();
        if (count > 0) {
            throw new MaginaException("商品分类名称已存在");
        }

        // 编码唯一性校验
        if (StringUtils.isNotBlank(itemCategory.getCode())) {
            Long codeCount = this.itemCategoryDomainService.lambdaQuery()
                .eq(ItemCategory::getCode, itemCategory.getCode())
                .ne(Objects.nonNull(itemCategory.getId()), ItemCategory::getId, itemCategory.getId())
                .count();
            if (codeCount > 0) {
                throw new MaginaException("商品分类编码已存在");
            }
        }
        // 同一分类下的属性名称不能重复
        if (CollectionUtils.isNotEmpty(itemCategory.getTagList())) {
            boolean repeat = itemCategory.getTagList().stream()
                .map(ItemCategoryTags::getName)
                .collect(Collectors.toSet()).size() != itemCategory.getTagList().size();
            if (repeat) {
                throw new MaginaException("属性名称存在重复内容");
            }
            for (ItemCategoryTags tag : itemCategory.getTagList()) {
                if (StringUtils.isBlank(tag.getName())) {
                    throw new MaginaException("属性名称存在空内容");
                }
                if (tag.getValue().stream().distinct().count() != tag.getValue().size()) {
                    throw new MaginaException(String.format("属性[%s]]存在重复值", tag.getName()));
                }
            }
        }
    }

    /**
     * 商品管理-分类管理修改
     *
     * @param itemCategory {@link ItemCategory}
     * @return true/false
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateById(ItemCategory itemCategory) {
        Assert.notNull(itemCategory.getId(), "数据id不能为空");
        //名称重复校验
        checkNameRepeat(itemCategory);
        // 修改分类名称
        this.itemCategoryDomainService.updateById(itemCategory);
        Set<Long> existTag = this.itemCategoryTagsDomainService.lambdaQuery()
            .eq(ItemCategoryTags::getClassifyId, itemCategory.getId()).list().stream()
            .map(ItemCategoryTags::getId).collect(Collectors.toSet());
        // 修改逻辑：已有数据id为空表示修改项，无数据id表示新增项，修改项与existTag取交集不在在existTag之外表示删除项
        if (CollectionUtils.isEmpty(itemCategory.getTagList())) {
            //逻辑删除
            this.itemCategoryTagsDomainService.remove(Wrappers.<ItemCategoryTags>lambdaQuery()
                .eq(ItemCategoryTags::getClassifyId, itemCategory.getId()));
        } else {
            Set<Long> change = itemCategory.getTagList().stream().map(ItemCategoryTags::getId).filter(Objects::nonNull).collect(Collectors.toSet());
            // 修改项与已有项取交集
            existTag.retainAll(change);
            if (CollectionUtils.isNotEmpty(existTag)) {
                // 逻辑删除非交集数据
                this.itemCategoryTagsDomainService.remove(Wrappers.<ItemCategoryTags>lambdaQuery()
                    .eq(ItemCategoryTags::getClassifyId, itemCategory.getId())
                    .notIn(ItemCategoryTags::getId, existTag));
            }
            itemCategory.getTagList().stream().forEach(e -> {
                if (Objects.isNull(e.getId())) {
                    e.setClassifyId(itemCategory.getId());
                    this.itemCategoryTagsDomainService.save(e);
                } else {
                    this.itemCategoryTagsDomainService.updateById(e);
                }
            });
        }
        return true;
    }

    /**
     * 商品管理-分类管理删除
     *
     * @param id id
     * @return true/false
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean removeById(Long id) {
        this.itemCategoryTagsDomainService.remove(Wrappers.<ItemCategoryTags>lambdaQuery()
            .eq(ItemCategoryTags::getClassifyId, id));
        return this.itemCategoryDomainService.removeById(id);
    }

    /**
     * 启用/禁用分类
     *
     * @param id 分类ID
     * @param enabled 是否启用 true-启用 false-禁用
     * @return true/false
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(Long id, Boolean enabled) {
        Assert.notNull(id, "分类ID不能为空");
        Assert.notNull(enabled, "启用状态不能为空");

        ItemCategory category = this.itemCategoryDomainService.getById(id);
        Assert.notNull(category, "分类不存在");

        category.setStatus(enabled); // 数据库中1表示启用，0表示禁用
        return this.itemCategoryDomainService.updateById(category);
    }

    /**
     * 批量启用/禁用分类
     *
     * @param ids 分类ID列表
     * @param enabled 是否启用 true-启用 false-禁用
     * @return true/false
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateStatus(List<Long> ids, Boolean enabled) {
        Assert.notEmpty(ids, "分类ID列表不能为空");
        Assert.notNull(enabled, "启用状态不能为空");

        // 检查所有分类是否存在
        List<ItemCategory> categories = this.itemCategoryDomainService.listByIds(ids);
        if (categories.size() != ids.size()) {
            throw new MaginaException("部分分类不存在");
        }

        // 批量更新状态
        return this.itemCategoryDomainService.lambdaUpdate()
            .in(ItemCategory::getId, ids)
            .set(ItemCategory::getStatus, enabled) // 数据库中1表示启用，0表示禁用
            .update();
    }
}
