package com.hightop.benyin.appupdate.application.service;

import com.hightop.benyin.appupdate.api.dto.UpdateCheckRequest;
import com.hightop.benyin.appupdate.api.dto.UpdateCheckResponse;
import com.hightop.benyin.appupdate.domain.service.AppVersionDomainService;
import com.hightop.benyin.appupdate.infrastructure.entity.AppVersion;
import com.hightop.magina.core.exception.MaginaException;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 应用更新服务
 * <AUTHOR>
 * @date 2025-01-29
 */
@Service
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Transactional(propagation = Propagation.NOT_SUPPORTED)
@Slf4j
public class AppUpdateService {

    AppVersionDomainService appVersionDomainService;
    
    /**
     * 检查应用更新
     * @param request 更新检查请求
     * @return 更新检查响应
     */
    public UpdateCheckResponse checkUpdate(UpdateCheckRequest request) {
        // 获取当前最新版本
        AppVersion latestVersion = appVersionDomainService.getLatestActiveVersion();
        
        if (latestVersion == null) {
            return new UpdateCheckResponse().setHasUpdate(false);
        }
        
        // 检查是否需要更新
        boolean needUpdate = shouldUpdate(request.getCurrentVersionCode(), latestVersion);
        
        if (!needUpdate) {
            return new UpdateCheckResponse().setHasUpdate(false);
        }
        
        // 构建更新响应
        UpdateCheckResponse response = new UpdateCheckResponse()
                .setHasUpdate(true)
                .setVersionName(latestVersion.getVersionName())
                .setVersionCode(latestVersion.getVersionCode())
                .setDownloadUrl(latestVersion.getCosUrl())  // 直接使用COS URL
                .setUpdateLog(latestVersion.getUpdateLog())
                .setIsForce(determineForceUpdate(request.getCurrentVersionCode(), latestVersion))
                .setFileSize(latestVersion.getFileSize())
                .setFileMd5(latestVersion.getFileMd5());
                
        return response;
    }
    
    /**
     * 下载APK文件
     * @param versionId 版本ID
     * @param request HTTP请求
     * @param response HTTP响应
     */
    @Transactional
    public void downloadApk(Long versionId, HttpServletRequest request, HttpServletResponse response) {
        AppVersion version = appVersionDomainService.getById(versionId);
        if (version == null || !version.getIsActive()) {
            throw new MaginaException("版本不存在或已禁用");
        }
        
        try {
            // 增加下载计数
            appVersionDomainService.incrementDownloadCount(versionId);

            // 重定向到COS下载链接
            response.sendRedirect(version.getCosUrl());

        } catch (IOException e) {
            log.error("下载APK文件失败", e);
            throw new MaginaException("下载失败");
        }
    }
    
    /**
     * 判断是否需要更新
     * @param currentVersionCode 当前版本号
     * @param latestVersion 最新版本
     * @return 是否需要更新
     */
    private boolean shouldUpdate(Integer currentVersionCode, AppVersion latestVersion) {
        // 管理员强制更新标志优先级最高
        if (latestVersion.getAdminForce()) {
            return true;
        }
        
        // 版本号比较
        return currentVersionCode < latestVersion.getVersionCode();
    }
    
    /**
     * 确定是否强制更新
     * @param currentVersionCode 当前版本号
     * @param latestVersion 最新版本
     * @return 是否强制更新
     */
    private Boolean determineForceUpdate(Integer currentVersionCode, AppVersion latestVersion) {
        // 管理员强制更新
        if (latestVersion.getAdminForce()) {
            return true;
        }
        
        // 版本配置的强制更新
        return latestVersion.getIsForce();
    }
    
    /**
     * 构建下载URL
     * @param versionId 版本ID
     * @return 下载URL
     */
    private String buildDownloadUrl(Long versionId) {
        return String.format("/app/download/%s", versionId.toString());
    }
    

}
