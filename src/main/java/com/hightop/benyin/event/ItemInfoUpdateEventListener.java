package com.hightop.benyin.event;

import com.alibaba.fastjson.JSON;
import com.hightop.benyin.components.item.ItemComponent;
import com.hightop.benyin.es.ElasticsearchCommonService;
import com.hightop.benyin.item.application.vo.ItemMatchPartProductVo;
import com.hightop.benyin.item.domain.service.ItemServiceDomain;
import com.hightop.benyin.item.infrastructure.enmu.ItemSaleStatusEnum;
import com.hightop.benyin.item.infrastructure.entity.Item;
import com.hightop.fario.base.util.StringUtils;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import java.util.List;

@Getter
@Component
public class ItemInfoUpdateEventListener {
    @Autowired
    private ElasticsearchCommonService elasticsearchCommonService;
    @Autowired
    private ItemComponent itemComponent;

    /**
     * 更新ES中的资源数据
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, value = ItemInfoUpdateEvent.class)
    public void ItemInfoUpdateEventImpl(ItemInfoUpdateEvent event) {
        Item item = event.getItem();

        itemComponent.initEsParams(item);

        elasticsearchCommonService.setEsDataItem(item);
    }
    /**
     * 上下架处理
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, value = ItemUpDownEvent.class)
    public void ItemUpDownEventImpl(ItemUpDownEvent event) {
        Item item = event.getItem();
        ItemSaleStatusEnum saleStatus = item.getSaleStatus();
        //下架
        if (ItemSaleStatusEnum.ON_SALE.getValue().equals(saleStatus.getValue())){

            itemComponent.initEsParams(item);

            elasticsearchCommonService.setEsDataItem(item);
        } else {
            elasticsearchCommonService.removeEsDataItem(item);
        }
    }
}
