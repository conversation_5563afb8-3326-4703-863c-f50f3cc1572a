package com.hightop.benyin.event;

import com.hightop.benyin.components.searchOutLog.CustomerSearchOutLogComponent;
import com.hightop.benyin.customer.application.service.CustomerSearchOutLogService;
import com.hightop.benyin.customer.infrastructure.entity.CustomerSearchOutLog;
import com.hightop.fario.base.util.StringUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
@Component
public class SearchOutLogEventListener implements ApplicationListener<SearchOutLogEvent> {

    CustomerSearchOutLogService customerSearchOutLogService;
    @Autowired
    private CustomerSearchOutLogComponent customerSearchOutLogComponent;
    @Override
    public void onApplicationEvent(SearchOutLogEvent event) {
        String customerId = event.getCustomerId();
        String source = event.getSource();
        String sourceType = event.getSourceType();
        String originParam = event.getOriginParam();
        Long total = event.getTotal();

        CustomerSearchOutLog save = new CustomerSearchOutLog();
        if (StringUtils.isNotEmpty(customerId) && !"null".equals(customerId)){
            save.setCustomerId(Long.valueOf(customerId));
        }
        save.setEventSource(source);
        save.setEventType(sourceType);
        save.setOriginParam(originParam);

        String paramsKey = save.getOriginParam() + save.getCustomerId() + save.getEventSource() + save.getEventType();
        boolean check =  customerSearchOutLogComponent.checkHashData(paramsKey,String.valueOf(total));
        if (check){
            save.setTotal(total);
            save.setCreateTime(new Date());

            customerSearchOutLogService.save(save);
        }
    }
}
