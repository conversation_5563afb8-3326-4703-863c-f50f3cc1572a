package com.hightop.benyin.spread.service.impl;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;

import com.hightop.fario.common.crypto.digest.Sm3Digest;
import org.apache.commons.collections4.map.HashedMap;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hightop.benyin.customer.application.vo.WeChatCustomerTokenVo;
import com.hightop.benyin.customer.domain.service.CustomerDomainService;
import com.hightop.benyin.customer.domain.service.CustomerStaffDomainService;
import com.hightop.benyin.customer.infrastructure.entity.Customer;
import com.hightop.benyin.customer.infrastructure.entity.CustomerStaff;
import com.hightop.benyin.customer.infrastructure.enums.MembershipLevel;
import com.hightop.benyin.share.application.vo.WeChatStaffTokenVo;
import com.hightop.benyin.share.domain.service.SequenceDomainService;
import com.hightop.benyin.share.domain.service.UserWeChatBindDomainService;
import com.hightop.benyin.share.domain.service.WeChatOauthDomainService;
import com.hightop.benyin.share.domain.service.WeChatPhoneNumberDomainService;
import com.hightop.benyin.share.infrastructure.entity.UserWeChatBind;
import com.hightop.benyin.share.infrastructure.restful.wechat.WeChatConstants;
import com.hightop.benyin.share.infrastructure.restful.wechat.sns.SessionResponse;
import com.hightop.benyin.spread.dto.ClientInfoDto;
import com.hightop.benyin.spread.dto.ClientInfoPageDto;
import com.hightop.benyin.spread.dto.EngineLoginDto;
import com.hightop.benyin.spread.dto.LoginValidateDto;
import com.hightop.benyin.spread.dto.TgLoginDto;
import com.hightop.benyin.spread.entity.ClientInfoEntity;
import com.hightop.benyin.spread.mapper.ClientInfoMapper;
import com.hightop.benyin.spread.service.ClientInfoService;
import com.hightop.benyin.spread.service.SseEmitterServer;
import com.hightop.benyin.spread.service.WeChatPhoneNumberEngineService;
import com.hightop.benyin.spread.util.LoginUtil;
import com.hightop.benyin.spread.util.PersonalComputerTerminal;
import com.hightop.benyin.spread.util.SpreadConfig;
import com.hightop.benyin.spread.vo.SpreadWeixinVo;
import com.hightop.fario.base.Trio;
import com.hightop.fario.base.web.Operation;
import com.hightop.fario.common.core.util.ExecutorUtils;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.fario.common.mybatis.util.PageHelper;
import com.hightop.magina.casual.counter.PasswordCounterDomainService;
import com.hightop.magina.casual.session.ApplicationSessions;
import com.hightop.magina.core.exception.MaginaException;
import com.hightop.magina.standard.cipher.CipherText;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import com.hightop.magina.standard.ums.system.LoggedUserVo;
import com.hightop.magina.standard.ums.system.SystemDomainService;
import com.hightop.magina.standard.ums.user.basic.UserBasic;
import com.hightop.magina.standard.ums.user.basic.UserBasicDomainService;
import com.hightop.magina.standard.ums.user.basic.UserState;
import com.hightop.magina.standard.ums.user.basic.UserType;
import com.hightop.magina.standard.ums.user.privacy.UserPrivacy;
import com.hightop.magina.standard.ums.user.privacy.UserPrivacyDomainService;
import com.hightop.magina.standard.ums.user.privacy.UserSex;
import com.hightop.magina.standard.ums.user.role.UserRole;
import com.hightop.magina.standard.ums.user.role.UserRoleMapper;

import cn.hutool.crypto.symmetric.DES;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * <p>
 * 推广客戶信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@Transactional(rollbackFor = Exception.class)
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
public class ClientInfoServiceImpl extends ServiceImpl<ClientInfoMapper, ClientInfoEntity> implements ClientInfoService {

	ClientInfoMapper clientInfoMapper;
	SystemDomainService systemDomainService;
	UserBasicDomainService userBasicDomainService;
	UserPrivacyDomainService userPrivacyDomainService;
	PasswordCounterDomainService passwordCounterDomainService;
	WeChatOauthDomainService weChatOauthDomainService;
	WeChatPhoneNumberDomainService weChatPhoneNumberDomainService;
	WeChatPhoneNumberEngineService weChatPhoneNumberEngineService;
	CustomerStaffDomainService customerStaffDomainService;
	CustomerDomainService customerDomainService;
	UserWeChatBindDomainService userWeChatBindDomainService;
	UserRoleMapper userRoleMapper;
	SequenceDomainService sequenceDomainService;

	private static final String SECRET_KEY = "12345678";
	private static final DES desAlgorithm = new DES(SECRET_KEY.getBytes());

	@Override
	public Object validate(LoginValidateDto loginDto) {
		Integer terminal = loginDto.getTermina();
		String authCode = loginDto.getAuthCode();
		SessionResponse auth = null;
		if (terminal == 0 || terminal == 2) {
			auth = this.weChatOauthDomainService.customerAuth(authCode);
		} else if (terminal == 1) {
			auth = this.weChatOauthDomainService.staffAuth(authCode);
		}
		if (auth == null)
			return new SpreadWeixinVo(-1l, null);
		String wechatId = auth.getOpenId();
		if (terminal == 0  || terminal == 2) {// 0:客户端 1:工程师端 2:管理后台
			return new SpreadWeixinVo(clientInfoMapper.selectCount(Wrappers.<ClientInfoEntity>lambdaQuery().eq(ClientInfoEntity::getClientWechatId, wechatId)), wechatId);
		} else if (terminal == 1) {
			return new SpreadWeixinVo(clientInfoMapper.selectCount(Wrappers.<ClientInfoEntity>lambdaQuery().eq(ClientInfoEntity::getEngineWechatId, wechatId)), wechatId);
		}
		return new SpreadWeixinVo(-1l, null);
	}

	// 创建管理账户信息
	private long manageEngine(String phone, String account, String password) {
		long userId = IdWorker.getId();
		LocalDateTime now = LocalDateTime.now();
		// 基础信息
		UserBasic userBasic = new UserBasic().setId(userId).setName(phone).setCode(account).setType(UserType.PERMANENT).setState(UserState.NORMAL).setIsAvailable(Boolean.TRUE).setIsBuildIn(Boolean.FALSE).setCreatedBy(ApplicationSessions.id()).setCreatedAt(now).setUpdatedAt(now);
		// 私有信息
		String salt = LoginUtil.generateSalt(16);
		UserPrivacy userPrivacy = new UserPrivacy().setId(userId).setSex(UserSex.UNKNOWN).setMobileNumber(CipherText.of(phone)).setSalt(salt);
		userPrivacy.setPassword(password);
		// 保存基本信息
		this.userBasicDomainService.save(userBasic);
		// 保存私密信息
		this.userPrivacyDomainService.save(userPrivacy);
		// 新增角色
		UserRole engineRole = new UserRole(userId, SpreadConfig.APPLET_ENGINE_EXPERIENCE);// 此处管理员角色
		userRoleMapper.insert(engineRole);
		return userId;
	}

	// 创建管理账户信息
	private long manageUser(String phone, String account, String password) {
		phone = "3" + phone.substring(1);
		long userId = IdWorker.getId();
		LocalDateTime now = LocalDateTime.now();
		// 基础信息
		UserBasic userBasic = new UserBasic().setId(userId).setName(phone).setCode(account).setType(UserType.PERMANENT).setState(UserState.NORMAL).setIsAvailable(Boolean.TRUE).setIsBuildIn(Boolean.FALSE).setCreatedBy(ApplicationSessions.id()).setCreatedAt(now).setUpdatedAt(now);
		// 私有信息
		String salt = LoginUtil.generateSalt(16);
		UserPrivacy userPrivacy = new UserPrivacy().setId(userId).setSex(UserSex.UNKNOWN).setMobileNumber(CipherText.of(phone)).setSalt(salt);
		userPrivacy.setPassword(password);
		// 保存基本信息
		this.userBasicDomainService.save(userBasic);
		// 保存私密信息
		this.userPrivacyDomainService.save(userPrivacy);
		// 新增角色
		UserRole manageRole = new UserRole(userId, SpreadConfig.CUSTOMER_EXPERIENCE);// 此处管理员角色
		userRoleMapper.insert(manageRole);
		return userId;
	}

	// 创建店铺信息
	private long clientUser(String phone) {
		// 注册用户 快速添加一个客户
		Customer customer = new Customer().setSource(new DictItemEntry().setValue(Customer.SOURCE_APPLET)).setSeqId(this.sequenceDomainService.nextDateSequence(Customer.SEQ_PREFIX, Customer.SEQ_LEN)).setName(phone).setShopRecruitment(phone + "的小店").setSubbranch("未登地址").setAddress("未登地址")
		        // 默认状态为注册
		        .setStatus(new DictItemEntry().setValue(Customer.REGISTER))
		        // 默认为注册用户
		        .setMembershipLevel(MembershipLevel.NORMAL).setLegalPersonTel(phone)
		        // 默认经营状态为营业中
		        .setBusinessStatus(new DictItemEntry().setValue(Customer.IN_BUSINESS))
		        // 默认行业属性为图文
		        .setIndustryAttr(new DictItemEntry().setValue(Customer.IMAGE_TEXT))
		        // 默认客户类型为单店
		        .setType(new DictItemEntry().setValue(Customer.SINGLE_SHOP)).setRegionCode(510107);
		customer.setName(StringUtils.isBlank(customer.getSubbranch()) ? customer.getShopRecruitment() : String.join("-", customer.getShopRecruitment(), customer.getSubbranch()));
		this.customerDomainService.save(customer);
		// 添加一个角色为老板的员工
		this.customerStaffDomainService.save(new CustomerStaff().setTel(phone).setCustomerId(customer.getId())
		        // 名字也设置为电话
		        .setName(phone).setRole(new DictItemEntry().setValue(CustomerStaff.BOSS_ROLE)).setStatus(Boolean.TRUE));
		// 绑定记录 可能用户登录过但是客户又禁用员工 导致存在绑定记录 但是无员工信息
		// this.userWeChatBindDomainService.save(new
		// UserWeChatBind().setUserId(Long.valueOf(phone)).setTerminal(WeChatConstants.Customer.TERMINAL).setBoundAt(LocalDateTime.now()));
		// openId
		// .setOpenId(auth.getOpenId()));
		return customer.getId();
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public Object login(TgLoginDto loginDto) {
		String telphone = loginDto.getTelphone();
		Integer loginType = loginDto.getType();
		// 终端类型(0:客户端 1:工程师端 2:管理后台)
		Integer terminal = loginDto.getTermina();
		String openId = loginDto.getWechatId();
		String serialNumber = loginDto.getSerialNumber();
		LocalDateTime now = LocalDateTime.now();
		ClientInfoEntity clientInfoEntity = null;
		if (StringUtils.isNotEmpty(telphone) && (terminal == 0 || terminal == 1 || terminal == 2)) {// 注册或部分更新
			// 先查询
			if (terminal == 0  || terminal == 2)// 客户端
				telphone = this.weChatPhoneNumberDomainService.getCustomerPhoneNumber(telphone);
			else if (terminal == 1)// 工程师端 管理后台
				telphone = this.weChatPhoneNumberEngineService.getStaffPhoneNumber(telphone);
			// 根据解密的电话号码 看能否查询到
			clientInfoEntity = clientInfoMapper.selectOne(Wrappers.<ClientInfoEntity>lambdaQuery().eq(ClientInfoEntity::getTelphone, telphone));
			if (StringUtils.isNotEmpty(openId)) {
				if (clientInfoEntity == null) {// 注册
					clientInfoEntity = new ClientInfoEntity();
					clientInfoEntity.setId(IdWorker.getId());
					clientInfoEntity.setType(loginType);
					clientInfoEntity.setTelphone(telphone);
					// 微信号设置
					if (terminal == 0 || terminal == 2)
						clientInfoEntity.setClientWechatId(openId);
					if (terminal == 1)
						clientInfoEntity.setEngineWechatId(openId);
					// 客户端处理
					String clientAccount = LoginUtil.convertToBase62();// 可能存在规则
					String clientPassword = LoginUtil.randomSecret();
					clientInfoEntity.setClientAccount(clientAccount);// 客户端规则
					clientInfoEntity.setClientPassword(clientPassword);// 客户端密码
					clientInfoEntity.setIsClientLogin((terminal == 0) ? 1 : 0);
					if (terminal == 0) {
						clientInfoEntity.setClientLoginTimes(1);
						clientInfoEntity.setClientLoginAt(LocalDateTime.now());
					} else {
						clientInfoEntity.setClientLoginTimes(0);
					}
					clientUser(telphone);
					// 工程师处理
					String engineAccount = "BYe" + LoginUtil.convertToBase62();// 可能存在规则
					String enginePassword = LoginUtil.randomSecret();
					clientInfoEntity.setEngineAccount(engineAccount);
					clientInfoEntity.setEnginePassword(enginePassword);
					clientInfoEntity.setIsEngineLogin((terminal == 1) ? 1 : 0);
					if (terminal == 1) {
						clientInfoEntity.setEngineLoginTimes(1);
						clientInfoEntity.setEngineLoginAt(LocalDateTime.now());
					} else {
						clientInfoEntity.setEngineLoginTimes(0);
					}
					manageEngine(telphone, engineAccount, enginePassword);
					// 管理后台处理
					String manageAccount = "BYm" + LoginUtil.convertToBase62();// 可能存在规则
					String managePassword = LoginUtil.randomSecret();
					clientInfoEntity.setManageAccount(manageAccount);
					clientInfoEntity.setManagePassword(managePassword);
					clientInfoEntity.setIsManageLogin((terminal == 2) ? 1 : 0);
					if (terminal == 2) {
						clientInfoEntity.setManageLoginTimes(1);
						clientInfoEntity.setManageLoginAt(LocalDateTime.now());
					} else {
						clientInfoEntity.setManageLoginTimes(0);
					}
					// 插入管理后台
					manageUser(telphone, manageAccount, managePassword);
					clientInfoEntity.setCreatedAt(now);
					clientInfoEntity.setUpdatedAt(now);
					clientInfoMapper.insert(clientInfoEntity);
				} else {// 更新
				        // 微信号设置
					if (terminal == 0 || terminal == 2)
						clientInfoEntity.setClientWechatId(openId);
					if (terminal == 1)
						clientInfoEntity.setEngineWechatId(openId);
					clientInfoEntity.setIsClientLogin((terminal == 0) ? 1 : 0);
					if (terminal == 0) {
						// 先查询
						UserWeChatBind userWeChatBind = this.userWeChatBindDomainService.getOneBind(Long.valueOf(telphone), WeChatConstants.Customer.TERMINAL);
						if (userWeChatBind == null)
							this.userWeChatBindDomainService.save(new UserWeChatBind().setUserId(Long.valueOf(telphone)).setTerminal(WeChatConstants.Customer.TERMINAL).setBoundAt(LocalDateTime.now()).setOpenId(openId));
						clientInfoEntity.setClientLoginTimes(clientInfoEntity.getClientLoginTimes() + 1);
						clientInfoEntity.setClientLoginAt(LocalDateTime.now());
					}
					clientInfoEntity.setIsEngineLogin((terminal == 1) ? 1 : 0);
					if (terminal == 1) {
						clientInfoEntity.setEngineLoginTimes(clientInfoEntity.getEngineLoginTimes() + 1);
						clientInfoEntity.setEngineLoginAt(LocalDateTime.now());
					}
					clientInfoEntity.setIsManageLogin((terminal == 2) ? 1 : 0);
					if (terminal == 2) {
						clientInfoEntity.setManageLoginTimes(clientInfoEntity.getManageLoginTimes() + 1);
						clientInfoEntity.setManageLoginAt(LocalDateTime.now());
					}
					clientInfoEntity.setUpdatedAt(now);
					clientInfoMapper.updateById(clientInfoEntity);
				}
			}
		}
		// 登录
		// 校验账户密码
		switch (terminal) {
		case 0:// 客户端
			if (StringUtils.isEmpty(openId))
				throw new MaginaException("微信ID不能为空");
			clientInfoEntity = clientInfoMapper.selectOne(Wrappers.<ClientInfoEntity>lambdaQuery().eq(ClientInfoEntity::getClientWechatId, openId));
			if (StringUtils.isNotEmpty(serialNumber))
				SseEmitterServer.sendMessage(serialNumber, desAlgorithm.encryptHex(clientInfoEntity.getTelphone()), clientInfoEntity.getEngineAccount(), clientInfoEntity.getEnginePassword(), clientInfoEntity.getManageAccount(),clientInfoEntity.getManagePassword());
			return login(openId, clientInfoEntity.getTelphone());
		case 1:// 工程师
			if (StringUtils.isEmpty(openId))
				throw new MaginaException("微信ID不能为空");
			clientInfoEntity = clientInfoMapper.selectOne(Wrappers.<ClientInfoEntity>lambdaQuery().eq(ClientInfoEntity::getEngineWechatId, openId));
			LoggedUserVo user = doManageLogin(clientInfoEntity.getEngineAccount(), clientInfoEntity.getEnginePassword());
			// 会话令牌
			String token = this.systemDomainService.doSession(user, WeChatConstants.Staff.TERMINAL, user.getId().toString());
			if (StringUtils.isNotEmpty(serialNumber))
				SseEmitterServer.sendMessage(serialNumber, desAlgorithm.encryptHex(clientInfoEntity.getTelphone()), clientInfoEntity.getEngineAccount(), clientInfoEntity.getEnginePassword(), clientInfoEntity.getManageAccount(),clientInfoEntity.getManagePassword());
			return new WeChatStaffTokenVo().setToken(token).setUser(user).setIsSubscribed(false);
		case 2:// 管理后台
			if (StringUtils.isNotEmpty(openId)) {
				clientInfoEntity = clientInfoMapper.selectOne(Wrappers.<ClientInfoEntity>lambdaQuery().eq(ClientInfoEntity::getClientWechatId, openId));
				Map<String, String> result = new HashedMap<>(1);
				result.put("s", desAlgorithm.encryptHex(clientInfoEntity.getTelphone()));
				if (StringUtils.isNotEmpty(serialNumber))
					SseEmitterServer.sendMessage(serialNumber, desAlgorithm.encryptHex(clientInfoEntity.getTelphone()), clientInfoEntity.getEngineAccount(), clientInfoEntity.getEnginePassword(), clientInfoEntity.getManageAccount(),clientInfoEntity.getManagePassword());
				return Operation.LOG_IN.ok(result);
			} else {
				throw new MaginaException("微信ID不能为空");
			}
		case 3:// web管理后台登录
			if (StringUtils.isEmpty(telphone))
				throw new MaginaException("手机号不能为空");
			try {
				telphone = desAlgorithm.decryptStr(telphone);
			} catch (Exception e) {
				throw new MaginaException("非法登录");
			}
			clientInfoEntity = clientInfoMapper.selectOne(Wrappers.<ClientInfoEntity>lambdaQuery().eq(ClientInfoEntity::getTelphone, telphone));
			LoggedUserVo loggedUserVo = doManageLogin(clientInfoEntity.getManageAccount(), clientInfoEntity.getManagePassword());
			// 会话信息生成及存储 pc端没有openId
			String mtoken = this.systemDomainService.doSession(loggedUserVo, PersonalComputerTerminal.CODE, null);
			if (StringUtils.isNotEmpty(serialNumber))
				SseEmitterServer.sendMessage(serialNumber, desAlgorithm.encryptHex(clientInfoEntity.getTelphone()), clientInfoEntity.getEngineAccount(), clientInfoEntity.getEnginePassword(), clientInfoEntity.getManageAccount(),clientInfoEntity.getManagePassword());
			return Operation.LOG_IN.ok(Trio.<String, LoggedUserVo, Boolean>builder().first(mtoken).second(loggedUserVo).third(false).build());
		default:
			throw new MaginaException("非法访问");
		}
	}

	// 客户端登录
	private WeChatCustomerTokenVo login(String openId, String phone) {
		WeChatCustomerTokenVo tokenVo = new WeChatCustomerTokenVo().setIsNew(false);
		UserWeChatBind bindUser = this.userWeChatBindDomainService.getById(openId);
		if (Objects.isNull(bindUser)) {
			// 绑定记录 可能用户登录过但是客户又禁用员工 导致存在绑定记录 但是无员工信息
			this.userWeChatBindDomainService.save(new UserWeChatBind().setUserId(Long.valueOf(phone)).setTerminal(WeChatConstants.Customer.TERMINAL).setBoundAt(LocalDateTime.now()).setOpenId(openId));
		}
		// 虚构用户
		LoggedUserVo user = this.dummyUser(Long.valueOf(phone));
		// 会话令牌
		String token = this.systemDomainService.doSession(user, WeChatConstants.Customer.TERMINAL, openId);
		return tokenVo.setToken(token).setIsSubscribed(Objects.nonNull(bindUser) && StringUtils.isNotEmpty(bindUser.getOfficialOpenId()));
	}

	// 管理后台登录
	private LoggedUserVo doManageLogin(String account, String password) {
		UserBasic userBasic = this.userBasicDomainService.getByCode(account);
		if (Objects.isNull(userBasic) || !userBasic.getIsAvailable())
			throw new MaginaException("帐号或密码错误");
		UserState state = UserState.state(userBasic.getState(), userBasic);
		// 状态判断
		switch (state) {
		case SUSPENDED:
			throw new MaginaException("账户已休眠，请联系管理员激活");
		case LOCKED:
			throw new MaginaException("账户已锁定，请联系管理员解锁或等待自动解锁");
		default:
			break;
		}
		UserPrivacy userPrivacy = this.userPrivacyDomainService.getNonNullById(userBasic.getId());
		// 登录时间
		LocalDateTime loginDateTime = LocalDateTime.now();
		// 登录日期
		LocalDate loginDate = loginDateTime.toLocalDate();
		if (!userPrivacy.isPasswordMatch(password))
			throw new MaginaException("帐号或密码错误");
		// 用户登录成功 异步操作
		ExecutorUtils.run(() -> {
			// 更新登录时间
			this.userBasicDomainService.lambdaUpdate().set(UserBasic::getLastLoginAt, loginDateTime).eq(UserBasic::getId, userBasic.getId()).update();
			// 密码错误次数重置
			this.passwordCounterDomainService.remove(userBasic.getId(), loginDate);
		});
		return new LoggedUserVo().setId(userBasic.getId()).setCode(userBasic.getCode()).setName(userBasic.getName()).setHeadImage(userPrivacy.getHeadImage());
	}

	// 虚构线程变量用户信息
	private LoggedUserVo dummyUser(Long phone) {
		// 虚构用户信息 id、帐号、名字均为手机号
		return new LoggedUserVo().setId(phone).setCode(String.valueOf(phone)).setName(String.valueOf(phone));
	}

	@Override
	public Integer update(ClientInfoDto clientInfo) {
		ClientInfoEntity clientInfoEntity = clientInfoMapper.selectOne(Wrappers.<ClientInfoEntity>lambdaQuery().eq(ClientInfoEntity::getId, clientInfo.getId()));
		if (clientInfoEntity == null)
			throw new MaginaException("客戶信息不存在");
		clientInfoEntity.setTrendType(clientInfo.getTrendType());
		clientInfoEntity.setComments(clientInfo.getComments());
		clientInfoEntity.setUpdatedBy(ApplicationSessions.id());
		Integer type = clientInfo.getType();
		if (type != null)
			clientInfoEntity.setType(type);
		String userName = clientInfo.getUserName();
		if (StringUtils.isNotEmpty(userName))
			clientInfoEntity.setUserName(userName);
		return clientInfoMapper.updateById(clientInfoEntity);
	}

	@Override
	public DataGrid<ClientInfoEntity> page(ClientInfoPageDto pageQuery) {
		return PageHelper.startPage(pageQuery, p -> this.clientInfoMapper.selectList(Wrappers.<ClientInfoEntity>lambdaQuery().eq(StringUtils.isNotBlank(pageQuery.getTelphone()), ClientInfoEntity::getTelphone, pageQuery.getTelphone()).eq(pageQuery.getType() != null, ClientInfoEntity::getType, pageQuery.getType()).eq(pageQuery.getTrendType() != null, ClientInfoEntity::getTrendType, pageQuery.getTrendType()).like(StringUtils.isNotBlank(pageQuery.getComments()), ClientInfoEntity::getComments, pageQuery.getComments()).orderByDesc(ClientInfoEntity::getUpdatedAt)));
	}

	@Override
	public Object loginEngine(EngineLoginDto loginDto) {
		LoggedUserVo user = doManageLogin(loginDto.getA(), loginDto.getP());
		// 会话令牌
		String token = this.systemDomainService.doSession(user, WeChatConstants.Staff.TERMINAL, user.getId().toString());
		return new WeChatStaffTokenVo().setToken(token).setUser(user).setIsSubscribed(false);
	}
}
