package com.hightop.benyin.machine.domain.service;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.machine.infrastructure.entity.Machine;
import com.hightop.benyin.machine.infrastructure.entity.MachineRepairReplace;
import com.hightop.benyin.machine.infrastructure.mapper.MachineMapper;
import com.hightop.benyin.machine.infrastructure.mapper.MachineRepairReplaceMapper;
import org.springframework.stereotype.Service;

/**
 * 机器维修换件领域服务
 *
 * <AUTHOR>
 * @date 2023-10-31 10:15:38
 */
@Service
public class MachineRepairReplaceDomainService extends MPJBaseServiceImpl<MachineRepairReplaceMapper, MachineRepairReplace> {

}
