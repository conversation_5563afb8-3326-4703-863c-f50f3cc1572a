package com.hightop.benyin.machine.domain.event;

import com.hightop.benyin.machine.infrastructure.entity.MachineInoutFlow;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * 机器流水事件
 *
 * <AUTHOR>
 * @date 2023/11/16 18:10
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Builder
@Getter
@Setter
public class MachineInoutEvent {

    @ApiModelProperty("流水明细")
    List<MachineInoutFlow> machineInoutFlowList;

}
