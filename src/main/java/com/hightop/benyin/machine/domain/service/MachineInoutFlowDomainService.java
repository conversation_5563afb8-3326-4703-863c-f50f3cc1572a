package com.hightop.benyin.machine.domain.service;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.machine.infrastructure.entity.MachineInoutFlow;
import com.hightop.benyin.machine.infrastructure.entity.MachinePurchase;
import com.hightop.benyin.machine.infrastructure.mapper.MachineInoutFlowMapper;
import com.hightop.benyin.machine.infrastructure.mapper.MachinePurchaseMapper;
import org.springframework.stereotype.Service;

/**
 * 机器采购单领域服务
 *
 * <AUTHOR>
 * @date 2023-10-31 10:15:38
 */
@Service
public class MachineInoutFlowDomainService extends MPJBaseServiceImpl<MachineInoutFlowMapper, MachineInoutFlow> {
}
