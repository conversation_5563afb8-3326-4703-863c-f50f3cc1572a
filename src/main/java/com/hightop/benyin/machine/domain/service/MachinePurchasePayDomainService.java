package com.hightop.benyin.machine.domain.service;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.machine.infrastructure.entity.MachinePurchasePay;
import com.hightop.benyin.machine.infrastructure.mapper.MachinePurchasePayMapper;
import org.springframework.stereotype.Service;

/**
 * 机器采购明细领域服务
 *
 * <AUTHOR>
 * @date 2023-10-31 10:15:38
 */
@Service
public class MachinePurchasePayDomainService extends MPJBaseServiceImpl<MachinePurchasePayMapper, MachinePurchasePay> {
}
