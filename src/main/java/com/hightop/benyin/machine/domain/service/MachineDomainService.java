package com.hightop.benyin.machine.domain.service;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.machine.infrastructure.entity.Machine;
import com.hightop.benyin.machine.infrastructure.mapper.MachineMapper;
import com.hightop.benyin.statistics.api.dto.FinanceReportQuery;
import com.hightop.benyin.statistics.application.vo.FinanceMachineInstoreVO;
import com.hightop.benyin.statistics.application.vo.FinanceMachineOutVO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 机器信息领域服务
 * <AUTHOR>
 * @date 2023-10-31 10:15:38
 */
@Service
public class MachineDomainService extends MPJBaseServiceImpl<MachineMapper, Machine> {

    public Machine getMachineByCode(String code) {
        return this.lambdaQuery().eq(Machine::getMachineNum, code).one();
    }


    /**
     *  机器出库明细表
     * @param query
     * @return
     */
    public List<FinanceMachineOutVO> getFinanceMachineOut(FinanceReportQuery query){
        return this.baseMapper.getFinanceMachineOut(query);
    }

    /**
     *  机器入库明细表
     * @param query
     * @return
     */
    public List<FinanceMachineInstoreVO> getFinanceMachineInstore(FinanceReportQuery query){
        return this.baseMapper.getFinanceMachineInstore(query);
    }
}
