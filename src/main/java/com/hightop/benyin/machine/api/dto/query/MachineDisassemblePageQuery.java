package com.hightop.benyin.machine.api.dto.query;

import com.hightop.fario.common.mybatis.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/2 18:43
 */
@ApiModel("机器查询")
@Data
public class MachineDisassemblePageQuery extends PageQuery {


    @ApiModelProperty("编码")
    String code;

    @ApiModelProperty("机器编码")
    String machineNum;

    @ApiModelProperty("原机器编号")
    String originCode;

    @ApiModelProperty("工程师姓名")
    String engineerName;

    @ApiModelProperty("oem")
    String numberOem;

    @ApiModelProperty("订单状态")
    String status;

    @ApiModelProperty("机型")
    private List<Long> productIds;

    @ApiModelProperty("创建时间-开始")
    String startDate;

    @ApiModelProperty("创建时间-结束")
    String endDate;

    @ApiModelProperty("工程师id")
    Long engineerId;

}
