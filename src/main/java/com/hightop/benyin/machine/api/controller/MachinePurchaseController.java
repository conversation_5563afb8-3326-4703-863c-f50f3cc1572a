package com.hightop.benyin.machine.api.controller;

import com.hightop.benyin.machine.api.dto.query.MachinePurchasePageQuery;
import com.hightop.benyin.machine.api.dto.vo.MachinePurchaseDetailDto;
import com.hightop.benyin.machine.api.dto.vo.MachinePurchaseDto;
import com.hightop.benyin.machine.api.dto.vo.MachinePurchasePayDto;
import com.hightop.benyin.machine.api.dto.vo.MachineReceiveDto;
import com.hightop.benyin.machine.application.service.MachinePurchaseService;
import com.hightop.benyin.machine.infrastructure.entity.Machine;
import com.hightop.benyin.machine.infrastructure.entity.MachinePurchase;
import com.hightop.fario.base.web.Operation;
import com.hightop.fario.base.web.RestResponse;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.magina.standard.behavior.operation.IgnoreOperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/12/11 16:49
 */
@RequestMapping("/machine/purchase")
@RestController
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Api(tags = "机器采购单管理")
public class MachinePurchaseController {

    MachinePurchaseService machinePurchaseService;

    @PostMapping("/page")
    @ApiOperation("机器采购单列表")
    @IgnoreOperationLog
    public RestResponse<DataGrid<MachinePurchase>> page(@RequestBody MachinePurchasePageQuery pageQuery) {
        return RestResponse.ok(this.machinePurchaseService.pageList(pageQuery));
    }

    /**
     * 采购单明细
     *
     * @return
     */
    @GetMapping("/{id}")
    @ApiOperation("机器采购单明细")
    public RestResponse<MachinePurchase> getById(@PathVariable("id") Long id) {
        return RestResponse.ok(this.machinePurchaseService.getById(id));
    }

    /**
     * 新增/修改机器采购单
     *
     * @return
     */
    @PostMapping
    @ApiOperation("新增/修改机器采购单")
    public RestResponse<Void> addPurchase(@RequestBody MachinePurchaseDto machinePurchaseAddDto) {
        return Operation.ADD.response(this.machinePurchaseService.add(machinePurchaseAddDto,null));
    }

    /**
     * 审核机器采购单
     * @return
     */
    @PutMapping("/audit")
    @ApiOperation("审核机器采购单")
    public RestResponse<Void> auditPurchase(@RequestBody  MachinePurchaseDto machinePurchaseAddDto) {
        return Operation.ADD.response(this.machinePurchaseService.add(machinePurchaseAddDto,machinePurchaseAddDto.getStatus()));
    }

    /**
     * 付款
     * @return
     */
    @PutMapping("/pay")
    @ApiOperation("付款")
    public RestResponse<Void> payment(@RequestBody @Valid MachinePurchasePayDto machinePurchasePayDto) {
        return Operation.ADD.response(this.machinePurchaseService.payment(machinePurchasePayDto));
    }

    /**
     * 关闭机器采购单
     *
     * @return
     */
    @DeleteMapping("/{id}")
    @ApiOperation("关闭机器采购单")
    public RestResponse<Void> deletePurchase(@PathVariable("id") Long id) {
        return Operation.ADD.response(this.machinePurchaseService.closeMachinePurchase(id));
    }

    /**
     * 机器取消采购
     *
     * @return
     */
    @DeleteMapping("/detail/{id}")
    @ApiOperation("机器取消采购")
    public RestResponse<Void> deleteMachine(@PathVariable("id") Long id) {
        return Operation.ADD.response(this.machinePurchaseService.cancelMachinePurchase(id));
    }


    /**
     * 修改机器信息
     *
     * @return
     */
    @PutMapping("/updateMachine")
    @ApiOperation("修改机器信息")
    public RestResponse<Void> updateMachine(@RequestBody MachinePurchaseDetailDto machinePurchaseDetailDto) {
        return Operation.ADD.response(this.machinePurchaseService.updatelMachinePurchase(machinePurchaseDetailDto));
    }


    /**
     * 确认收货
     *
     * @return
     */
    @GetMapping("/receiveInfo/{id}")
    @ApiOperation("确认收货信息")
    public RestResponse<MachinePurchase> receiveInfo(@PathVariable("id") Long id) {
        return RestResponse.ok(this.machinePurchaseService.getReceiveMachineList(id));
    }


    /**
     * 确认收货
     *
     * @return
     */
    @PostMapping("/receive")
    @ApiOperation("收货入库")
    public RestResponse<Void> receive(@RequestBody MachineReceiveDto machineReceiveDto) {
        return Operation.ADD.response(this.machinePurchaseService.receiveMachine(machineReceiveDto));
    }


}
