package com.hightop.benyin.machine.api.controller;

import com.hightop.benyin.machine.api.dto.query.MachinePageQuery;
import com.hightop.benyin.machine.api.dto.vo.MachineTotalVo;
import com.hightop.benyin.machine.application.service.MachineService;
import com.hightop.benyin.machine.infrastructure.entity.Machine;
import com.hightop.fario.base.web.Operation;
import com.hightop.fario.base.web.RestResponse;
import com.hightop.fario.common.core.annotation.Anonymous;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.magina.standard.behavior.operation.IgnoreOperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @Date 2023/12/11 16:49
 */
@RequestMapping("/machine")
@RestController
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Api(tags = "机器管理")
public class MachineController {

    MachineService machineService;

    @PostMapping("/page")
    @ApiOperation("机器列表")
    @IgnoreOperationLog
    public RestResponse<DataGrid<Machine>> page(@RequestBody MachinePageQuery pageQuery) {
        return RestResponse.ok(this.machineService.pageList(pageQuery));
    }

    /**
     * 机器明细
     *
     * @return
     */
    @GetMapping("/{id}")
    @ApiOperation("机器明细")
    public RestResponse<Machine> getById(@PathVariable("id") Long id) {
        return RestResponse.ok(this.machineService.getById(id));
    }


    /**
     * 机器汇总
     *
     * @return
     */
    @GetMapping("/summary")
    @ApiOperation("机器汇总")
    public RestResponse<MachineTotalVo> getMachineTotal() {
        return RestResponse.ok(this.machineService.getMachineTotal());
    }

    /**
     * 新增/修改机器采购单
     *
     * @return
     */
    @PostMapping
    @ApiOperation("新增/修改机器")
    public RestResponse<Void> addPurchase(@RequestBody Machine machine) {
        return Operation.ADD.response(this.machineService.add(machine));
    }

    /**
     * 新增/修改机器采购单
     *
     * @return
     */
    @PostMapping("/updateLocation")
    @ApiOperation("修改储位")
    public RestResponse<Void> updateLocation(@RequestBody Machine machine) {
        return Operation.ADD.response(this.machineService.updateLocation(machine));
    }

    /**
     * 导出机器数据
     *
     * @param response
     * @param pageQuery
     * @return
     */
    @ApiOperation("/导出机器数据")
    @PostMapping("/export")
    public RestResponse<Void> export(HttpServletResponse response, @RequestBody MachinePageQuery pageQuery) throws IOException {
        Boolean b = machineService.downloadData(response, pageQuery);
        if (!b) {
            return new RestResponse(500, "导出失败，请联系技术人员！", null, null);
        }
        return RestResponse.message("导出成功");
    }

    /**
     * 下载入库模板
     *
     * @param response
     * @return
     */
    @Anonymous
    @ApiOperation("下载入库模板")
    @GetMapping("/downTemplate")
    public RestResponse downTemplate(HttpServletResponse response) {
        Boolean b = machineService.downTemplate(response);
        if (!b) {
            return new RestResponse(500, "下载失败", null, null);
        }
        return RestResponse.ok("下载成功");
    }

    /**
     * 导入机器数据
     *
     * @param file
     * @return
     * @throws Exception
     */
    @PostMapping("/import")
    @ApiOperation(value = "导入机器数据")
    public RestResponse<Void> impor(@RequestParam("file") MultipartFile file) throws Exception {
        boolean b = this.machineService.importData(file);
        if (!b) {
            return new RestResponse(500, "解析失败", null, null);
        }
        return RestResponse.message("导入成功");
    }

}
