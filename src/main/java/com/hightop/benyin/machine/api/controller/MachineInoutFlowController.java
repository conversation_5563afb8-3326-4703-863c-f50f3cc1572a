package com.hightop.benyin.machine.api.controller;

import com.hightop.benyin.machine.api.dto.query.MachineInoutQuery;
import com.hightop.benyin.machine.application.service.MachineInoutFlowService;
import com.hightop.benyin.machine.infrastructure.entity.MachineInoutFlow;
import com.hightop.fario.base.web.Operation;
import com.hightop.fario.base.web.RestResponse;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.magina.standard.behavior.operation.IgnoreOperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Date 2023/12/11 16:49
 */
@RequestMapping("/machine-flow")
@RestController
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Api(tags = "机器出入库流水管理")
public class MachineInoutFlowController {

    MachineInoutFlowService machineInoutFlowService;

    @PostMapping("/page")
    @ApiOperation("机器出入库流水列表")
    @IgnoreOperationLog
    public RestResponse<DataGrid<MachineInoutFlow>> page(@RequestBody MachineInoutQuery pageQuery) {
        return RestResponse.ok(this.machineInoutFlowService.minePage(pageQuery));
    }

    /**
     * 机器明细
     *
     * @return
     */
    @GetMapping("/{id}")
    @ApiOperation("机器出入库流水明细")
    public RestResponse<MachineInoutFlow> getById(@PathVariable("id") Long id) {
        return RestResponse.ok(this.machineInoutFlowService.getById(id));
    }


}
