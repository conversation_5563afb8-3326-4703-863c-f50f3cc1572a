package com.hightop.benyin.machine.api.controller;

import com.hightop.benyin.machine.api.dto.query.MachineDisassemblePageQuery;
import com.hightop.benyin.machine.api.dto.vo.MachineDisassembleAuditDto;
import com.hightop.benyin.machine.application.service.MachineDisassembleService;
import com.hightop.benyin.machine.infrastructure.entity.MachineDisassemble;
import com.hightop.fario.base.web.Operation;
import com.hightop.fario.base.web.RestResponse;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.magina.standard.behavior.operation.IgnoreOperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Date 2023/12/11 16:49
 */
@RequestMapping("/machine-disassemble")
@RestController
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Api(tags = "机器拆机管理")
public class MachineDisassembleController {

    MachineDisassembleService machineDisassembleService;

    @PostMapping("/page")
    @ApiOperation("机器拆机列表")
    @IgnoreOperationLog
    public RestResponse<DataGrid<MachineDisassemble>> page(@RequestBody MachineDisassemblePageQuery pageQuery) {
        return RestResponse.ok(this.machineDisassembleService.minePage(pageQuery));
    }

    /**
     * 机器明细
     *
     * @return
     */
    @GetMapping("/{id}")
    @ApiOperation("机器拆机明细")
    public RestResponse<MachineDisassemble> getById(@PathVariable("id") Long id) {
        return RestResponse.ok(this.machineDisassembleService.getById(id));
    }

    @GetMapping("/getByCode/{code}")
    @ApiOperation("根据编码获取机器拆机明细")
    public RestResponse<MachineDisassemble> getByCode(@PathVariable("code") String code) {
        return RestResponse.ok(this.machineDisassembleService.getByCode(code));
    }

    /**
     * 新增机器拆机
     *
     * @return
     */
    @PostMapping
    @ApiOperation("新增/修改机器拆机")
    public RestResponse<Void> addRepair(@Validated @RequestBody MachineDisassemble machineDisassemble) {
        return Operation.ADD.response(this.machineDisassembleService.save(machineDisassemble));

    }


    /**
     * 机器拆机审核
     *
     * @return
     */
    @PutMapping
    @ApiOperation("机器拆机审核")
    public RestResponse<Void> audit(@Validated @RequestBody MachineDisassembleAuditDto machineDisassembleAuditDto) {
        return Operation.UPDATE.response(this.machineDisassembleService.audit(machineDisassembleAuditDto));
    }


}
