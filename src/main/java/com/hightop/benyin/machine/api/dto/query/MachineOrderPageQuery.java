package com.hightop.benyin.machine.api.dto.query;

import com.baomidou.mybatisplus.annotation.TableField;
import com.hightop.fario.common.mybatis.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/2 18:43
 */
@ApiModel("机器查询")
@Data
public class MachineOrderPageQuery extends PageQuery {


    @ApiModelProperty("订单号")
    String orderNum;

    @ApiModelProperty("电话号码")
    String consigneePhone;

    @ApiModelProperty("服务类型")
    List<String> serType;

    @ApiModelProperty("结算方式")
    List<String> settleMethod;

    @ApiModelProperty("订单状态")
    List<String> status;

    @ApiModelProperty("客户名称")
    String customerName;

    @ApiModelProperty("客户编号")
    String customerSeqId;

    @ApiModelProperty("机型")
    private List<Long> productIds;

    @ApiModelProperty("创建时间-开始")
    String startDate;

    @ApiModelProperty("创建时间-结束")
    String endDate;



}
