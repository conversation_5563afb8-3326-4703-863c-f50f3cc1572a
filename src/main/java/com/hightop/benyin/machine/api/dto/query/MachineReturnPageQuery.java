package com.hightop.benyin.machine.api.dto.query;

import com.hightop.fario.common.mybatis.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/2 18:43
 */
@ApiModel("机器采购单查询")
@Data
public class MachineReturnPageQuery extends PageQuery {

    @ApiModelProperty("采购单号")
    String code;

    @ApiModelProperty("采购单号")
    String purchaseCode;

    @ApiModelProperty("供应商名称")
    String manufacturerName;

    @ApiModelProperty("状态")
    List<String> status;

    @ApiModelProperty("创建时间-开始")
    String startDate;

    @ApiModelProperty("创建时间-结束")
    String endDate;

}
