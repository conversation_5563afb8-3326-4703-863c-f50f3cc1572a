package com.hightop.benyin.machine.api.dto.query;

import com.hightop.benyin.machine.infrastructure.enums.MachineInOutType;
import com.hightop.fario.common.mybatis.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/2 18:43
 */
@ApiModel("机器查询")
@Data
public class MachineInoutQuery extends PageQuery {


    @ApiModelProperty("机器编码")
    String machineNum;

    @ApiModelProperty("类型:  1入库  2出库")
    Integer inOutType;

    @ApiModelProperty("出入库类型")
    String sourceType;

    @ApiModelProperty("关联单号")
    String sourceCode;

    @ApiModelProperty("创建时间-开始")
    String startDate;

    @ApiModelProperty("创建时间-结束")
    String endDate;


}
