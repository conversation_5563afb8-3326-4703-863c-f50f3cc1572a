package com.hightop.benyin.machine.api.dto.query;

import com.baomidou.mybatisplus.annotation.TableField;
import com.hightop.fario.common.mybatis.PageQuery;
import com.hightop.magina.standard.code.dictionary.bind.DictItemBind;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/2 18:43
 */
@ApiModel("机器查询")
@Data
public class MachinePageQuery extends PageQuery {


    @ApiModelProperty("机型")
    private List<Long> productIds;

    @ApiModelProperty("机型id")
    private Long productId;

    @ApiModelProperty("制造商")
    String manufacturerName;

    @ApiModelProperty("机器编码")
    String machineNum;

    @ApiModelProperty("绑定机器编码")
    String bindMachine;

    @ApiModelProperty("主机型号")
    String productName;

    @ApiModelProperty("标签型号")
    String tagName;

    @ApiModelProperty("原机器编号")
    String originCode;

    @ApiModelProperty("机器序列号")
    String deviceSequence;

    @ApiModelProperty("储位")
    String location;

    @ApiModelProperty("主机类型(字典项码)")
    List<String> hostTypes;

    @ApiModelProperty("选配件ids")
    List<Long> accessoryIds;

    @ApiModelProperty("主机类型(字典项码)")
    String hostType;

    @ApiModelProperty("设备新旧(字典项码)")
    List<String> deviceOns;

    @ApiModelProperty("设备新旧(字典项码)")
    String deviceOn;

    @ApiModelProperty("成色")
    List<String> percentage;

    @ApiModelProperty("设备状态 ")
    List<String> deviceStatusList;

    @ApiModelProperty("设备状态单选 ")
    String deviceStatus;

    @ApiModelProperty("状态 ")
    List<String> status;

    @ApiModelProperty("排除状态 ")
    List<String> notInStatus;

    @ApiModelProperty("是否上架")
    Boolean isSale;

    @ApiModelProperty("是否关联")
    Boolean isBind;

    @ApiModelProperty("排除主机类型")
    String existType;

    @ApiModelProperty("创建时间-开始")
    String startDate;

    @ApiModelProperty("创建时间-结束")
    String endDate;


    @ApiModelProperty("黑白计数器-开始")
    String startBlackCounter;

    @ApiModelProperty("黑白计数器-结束")
    String endBlackCounter;

    @ApiModelProperty("彩色计数器-开始")
    String startColorCounter;

    @ApiModelProperty("彩色计数器-结束")
    String endColorCounter;

    @ApiModelProperty("五色计数器-开始")
    String startFiveColourCounter;

    @ApiModelProperty("五色计数器-结束")
    String endFiveColourCounter;

}
