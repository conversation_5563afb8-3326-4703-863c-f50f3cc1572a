package com.hightop.benyin.machine.api.controller;

import com.hightop.benyin.machine.api.dto.query.ComponentRepairPageQuery;
import com.hightop.benyin.machine.api.dto.vo.ComponentRepairAddDto;
import com.hightop.benyin.machine.api.dto.vo.ComponentRepairAuditDto;
import com.hightop.benyin.machine.api.dto.vo.ComponentRepairDto;
import com.hightop.benyin.machine.application.service.ComponentRepairService;
import com.hightop.benyin.machine.infrastructure.entity.ComponentRepair;
import com.hightop.fario.base.web.Operation;
import com.hightop.fario.base.web.RestResponse;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.magina.standard.behavior.operation.IgnoreOperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Date 2023/12/11 16:49
 */
@RequestMapping("/component-repair")
@RestController
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Api(tags = "组件维修管理")
public class ComponentRepairController {

    ComponentRepairService componentRepairService;

    @PostMapping("/page")
    @ApiOperation("组件维修列表")
    @IgnoreOperationLog
    public RestResponse<DataGrid<ComponentRepair>> page(@RequestBody ComponentRepairPageQuery pageQuery) {
        return RestResponse.ok(this.componentRepairService.minePage(pageQuery));
    }

    /**
     * 机器明细
     *
     * @return
     */
    @GetMapping("/{id}")
    @ApiOperation("组件维修明细")
    public RestResponse<ComponentRepair> getById(@PathVariable("id") Long id) {
        return RestResponse.ok(this.componentRepairService.getById(id));
    }

    @GetMapping("/getByCode/{code}")
    @ApiOperation("根据单号查询组件维修明细")
    public RestResponse<ComponentRepair> getById(@PathVariable("code") String code) {
        return RestResponse.ok(this.componentRepairService.getByCode(code));
    }

    /**
     * 新增/修改机器采购单
     *
     * @return
     */
    @PostMapping
    @ApiOperation("新增组件维修")
    public RestResponse<Void> addRepair(@Validated @RequestBody ComponentRepairAddDto componentRepairAddDto) {
        return Operation.ADD.response(this.componentRepairService.repairMachine(componentRepairAddDto));

    }


    /**
     * 组件维修确认
     *
     * @return
     */
    @PutMapping("/audit")
    @ApiOperation("组件维修确认")
    public RestResponse<Void> audit(@Validated @RequestBody ComponentRepairAuditDto auditDto) {
        return Operation.UPDATE.response(this.componentRepairService.confirm(auditDto));
    }

    /**
     * 新增/修改机器采购单
     *
     * @return
     */
    @PostMapping("/commit")
    @ApiOperation("提交维修报告")
    public RestResponse<Void> commitReport(@Validated @RequestBody ComponentRepairDto componentRepairDto) {
        return Operation.ADD.response(this.componentRepairService.reportCommit(componentRepairDto));

    }

}
