package com.hightop.benyin.machine.api.controller;

import com.hightop.benyin.machine.api.dto.query.MachineReturnPageQuery;
import com.hightop.benyin.machine.api.dto.vo.MachineRepairAuditDto;
import com.hightop.benyin.machine.api.dto.vo.MachineReturnDto;
import com.hightop.benyin.machine.application.service.MachineReturnService;
import com.hightop.benyin.machine.infrastructure.entity.MachineReturn;
import com.hightop.fario.base.web.Operation;
import com.hightop.fario.base.web.RestResponse;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.magina.standard.behavior.operation.IgnoreOperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Date 2023/12/11 16:49
 */
@RequestMapping("/machine/return")
@RestController
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Api(tags = "机器采购退货管理")
public class MachineReturnController {

    MachineReturnService machineReturnService;

    @PostMapping("/page")
    @ApiOperation("机器采购单退货列表")
    @IgnoreOperationLog
    public RestResponse<DataGrid<MachineReturn>> page(@RequestBody MachineReturnPageQuery pageQuery) {
        return RestResponse.ok(this.machineReturnService.pageList(pageQuery));
    }

    /**
     * 采购单明细
     *
     * @return
     */
    @GetMapping("/{id}")
    @ApiOperation("机器采购单退货明细")
    public RestResponse<MachineReturn> getById(@PathVariable("id") Long id) {
        return RestResponse.ok(this.machineReturnService.getById(id));
    }

    /**
     * 新增/修改机器采购单
     *
     * @return
     */
    @PostMapping
    @ApiOperation("新增/修改机器采购单退货")
    public RestResponse<Void> addPurchase(@Validated  @RequestBody MachineReturnDto machineReturnAddDto) {
        return Operation.ADD.response(this.machineReturnService.add(machineReturnAddDto));
    }

    /**
     * 审核机器采购单
     *
     * @return
     */
    @PutMapping("/audit")
    @ApiOperation("审核机器采购单退货")
    public RestResponse<Void> auditPurchase(@RequestBody @Validated MachineRepairAuditDto machineRepairAuditDto) {
        return Operation.ADD.response(this.machineReturnService.audit(machineRepairAuditDto));
    }

    /**
     * 关闭机器采购单
     *
     * @return
     */
    @DeleteMapping("/{id}")
    @ApiOperation("关闭机器采购单退货")
    public RestResponse<Void> close(@PathVariable("id") Long id) {
        return Operation.ADD.response(this.machineReturnService.close(id));
    }


    /**
     * 机器采购单退款完成
     *
     * @return
     */
    @PutMapping("/{id}")
    @ApiOperation("机器采购单退款完成 ")
    public RestResponse<Void> refund(@PathVariable("id") Long id) {
        return Operation.ADD.response(this.machineReturnService.refund(id));
    }


}
