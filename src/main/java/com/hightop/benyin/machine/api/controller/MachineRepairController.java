package com.hightop.benyin.machine.api.controller;

import com.hightop.benyin.machine.api.dto.query.MachineRepairPageQuery;
import com.hightop.benyin.machine.api.dto.vo.MachineRepairAddDto;
import com.hightop.benyin.machine.api.dto.vo.MachineRepairAuditDto;
import com.hightop.benyin.machine.api.dto.vo.MachineRepairDto;
import com.hightop.benyin.machine.application.service.MachineRepairService;
import com.hightop.benyin.machine.infrastructure.entity.MachineRepair;
import com.hightop.fario.base.web.Operation;
import com.hightop.fario.base.web.RestResponse;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.magina.standard.behavior.operation.IgnoreOperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @Date 2023/12/11 16:49
 */
@RequestMapping("/machine-repair")
@RestController
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Api(tags = "机器维修管理")
public class MachineRepairController {

    MachineRepairService machineRepairService;

    @PostMapping("/page")
    @ApiOperation("机器维修列表")
    @IgnoreOperationLog
    public RestResponse<DataGrid<MachineRepair>> page(@RequestBody MachineRepairPageQuery pageQuery) {
        return RestResponse.ok(this.machineRepairService.minePage(pageQuery));
    }

    @ApiOperation("/导出机器维修列表")
    @PostMapping("/export")
    @IgnoreOperationLog
    public RestResponse<Void> download(HttpServletResponse response, @RequestBody MachineRepairPageQuery pageQuery) {
        Boolean b = machineRepairService.downloadMachineRepairs(response, pageQuery);
        if (!b) {
            return new RestResponse<>(500, "导出失败，请联系技术人员！", null, null);
        }
        return RestResponse.message("导出成功");
    }

    /**
     * 机器明细
     *
     * @return
     */
    @GetMapping("/{id}")
    @ApiOperation("机器维修明细")
    public RestResponse<MachineRepair> getById(@PathVariable("id") Long id) {
        return RestResponse.ok(this.machineRepairService.getById(id));
    }

    /**
     * 新增/修改机器采购单
     *
     * @return
     */
    @PostMapping
    @ApiOperation("新增机器维修")
    public RestResponse<Void> addRepair(@Validated @RequestBody MachineRepairAddDto machineRepairAddDto) {
        return Operation.ADD.response(this.machineRepairService.repairMachine(machineRepairAddDto));

    }


    /**
     * 机器维修确认
     *
     * @return
     */
    @PutMapping("/audit")
    @ApiOperation("机器维修确认")
    public RestResponse<Void> audit(@Validated @RequestBody MachineRepairAuditDto auditDto) {
        return Operation.UPDATE.response(this.machineRepairService.confirm(auditDto));
    }

    /**
     * 新增/修改机器采购单
     *
     * @return
     */
    @PostMapping("/commit")
    @ApiOperation("提交维修报告")
    public RestResponse<Void> commitReport(@Validated @RequestBody MachineRepairDto machineRepairDto) {
        return Operation.ADD.response(this.machineRepairService.reportCommit(machineRepairDto));
    }

}
