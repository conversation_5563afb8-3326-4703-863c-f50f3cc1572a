package com.hightop.benyin.machine.api.dto.query;

import com.hightop.fario.common.mybatis.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/2 18:43
 */
@ApiModel("机器查询")
@Data
public class ComponentRepairPageQuery extends PageQuery {

    @ApiModelProperty("物品编码")
    String articleCode;

    @ApiModelProperty("物品名称")
    String articleName;

    @ApiModelProperty("维修单号")
    String code;

    @ApiModelProperty("原机器编号")
    String originCode;

    @ApiModelProperty("工程师姓名")
    String engineerName;

    @ApiModelProperty("审核人")
    String auditName;

    @ApiModelProperty("订单状态")
    String status;

    @ApiModelProperty("工程师id")
    Long engineerId;


    @ApiModelProperty("创建时间-开始")
    String startDate;

    @ApiModelProperty("创建时间-结束")
    String endDate;

    @ApiModelProperty("oem编号")
    String numberOem;



}
