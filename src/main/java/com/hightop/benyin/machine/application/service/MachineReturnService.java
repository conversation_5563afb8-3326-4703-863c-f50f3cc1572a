package com.hightop.benyin.machine.application.service;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.toolkit.MPJWrappers;
import com.hightop.benyin.machine.api.dto.query.MachineReturnPageQuery;
import com.hightop.benyin.machine.api.dto.vo.MachineRepairAuditDto;
import com.hightop.benyin.machine.api.dto.vo.MachineReturnDto;
import com.hightop.benyin.machine.domain.service.*;
import com.hightop.benyin.machine.infrastructure.entity.*;
import com.hightop.benyin.machine.infrastructure.enums.MachineStatus;
import com.hightop.benyin.machine.infrastructure.enums.PayStatus;
import com.hightop.benyin.machine.infrastructure.enums.ReturnStatus;
import com.hightop.benyin.machine.infrastructure.enums.ReturnType;
import com.hightop.benyin.product.domain.service.ProductAccessoryDomainService;
import com.hightop.benyin.product.infrastructure.entity.ProductAccessory;
import com.hightop.benyin.purchase.infrastructure.enums.RefundTypeEnum;
import com.hightop.benyin.share.domain.service.SequenceDomainService;
import com.hightop.benyin.storage.domain.service.ManufacturerServiceDomain;
import com.hightop.benyin.storage.infrastructure.entity.Manufacturer;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.fario.common.mybatis.util.PageHelper;
import com.hightop.magina.casual.session.ApplicationSessions;
import com.hightop.magina.core.exception.MaginaException;
import com.hightop.magina.standard.ums.user.bind.UserEntry;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/1/2 17:47
 */
@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class MachineReturnService {

    MachineReturnDomainService machineReturnDomainService;
    MachineReturnDetailDomainService machineReturnDetailDomainService;
    SequenceDomainService sequenceDomainService;
    ProductAccessoryDomainService productAccessoryDomainService;
    MachinePurchaseDomainService machinePurchaseDomainService;
    MachinePurchaseDetailDomainService machinePurchaseDetailDomainService;
    MachineDomainService machineDomainService;
    ApplicationEventPublisher applicationEventPublisher;
    ManufacturerServiceDomain manufacturerServiceDomain;

    public DataGrid<MachineReturn> pageList(MachineReturnPageQuery pageQuery) {
        return PageHelper.startPage(pageQuery, p ->
                machineReturnDomainService.selectJoinList(MachineReturn.class, MPJWrappers.lambdaJoin()
                        .selectAll(MachineReturn.class)
                        .selectAs(Manufacturer::getName, MachineReturn::getManufacturerName)
                        .leftJoin(Manufacturer.class, Manufacturer::getId, MachineReturn::getManufacturerId)
                        .like(StringUtils.isNotBlank(pageQuery.getManufacturerName()), Manufacturer::getName, pageQuery.getManufacturerName())
                        .like(StringUtils.isNotBlank(pageQuery.getPurchaseCode()), MachineReturn::getPurchaseCode, pageQuery.getPurchaseCode())
                        .in(CollectionUtils.isNotEmpty(pageQuery.getStatus()), MachineReturn::getStatus, pageQuery.getStatus())
                        .ge(StringUtils.isNotBlank(pageQuery.getStartDate()), MachineReturn::getCreatedAt, pageQuery.getStartDate() + " 00:00:00")
                        .le(StringUtils.isNotBlank(pageQuery.getEndDate()), MachineReturn::getCreatedAt, pageQuery.getEndDate() + " 23:59:59")
                        .orderByDesc(Machine::getCreatedAt)
                )
        );
    }

    /**
     * 机器采购退货
     *
     * @param machineReturnAddDto
     * @return
     */
    public Boolean add(MachineReturnDto machineReturnAddDto) {
        MachineReturn machineReturn = null;
        if (machineReturnAddDto.getId() != null) {
            machineReturn = this.getById(machineReturnAddDto.getId());
            //删除明细表
            machineReturnDetailDomainService.remove(Wrappers.<MachineReturnDetail>lambdaQuery()
                    .eq(MachineReturnDetail::getPurchaseCode, machineReturn.getPurchaseCode()));
        } else {
            String code = sequenceDomainService.nextDateSequence("CGR", 4);
            machineReturn = new MachineReturn();
            machineReturn.setCreatedBy(new UserEntry().setId(ApplicationSessions.id()));
            machineReturn.setCode(code);
        }
        MachinePurchase machinePurchase = machinePurchaseDomainService.lambdaQuery()
                .eq(MachinePurchase::getPurchaseCode, machineReturnAddDto.getPurchaserCode()).one();
        machineReturn.setManufacturerId(machinePurchase.getManufacturerId());
        machineReturn.setRefundType(machineReturnAddDto.getRefundType());
        machineReturn.setStatus(ReturnStatus.WAIT_APPROVE);
        machineReturn.setPurchaseCode(machineReturnAddDto.getPurchaserCode());
        machineReturn.setCreatedBy(new UserEntry().setId(ApplicationSessions.id()));
        List<MachineReturnDetail> machineReturnDetails = machineReturnAddDto.getMachineReturnDetails();
        for (MachineReturnDetail machineReturnDetail : machineReturnDetails) {
            machineReturnDetail.setId(null);
            machineReturnDetail.setReturnCode(machineReturn.getCode());
            machineReturnDetail.setPurchaseCode(machineReturnAddDto.getPurchaserCode());
            if(machineReturnDetail.getSourceId()!=null){
                machineReturnDetail.setPurchaseDetailId(machineReturnDetail.getSourceId());
            }
            machineReturnDetail.setCreatedBy(machineReturn.getCreatedBy());
            if (StringUtils.isNotBlank(machineReturnDetail.getMachineNum())) {
                Machine machine = machineDomainService.lambdaQuery()
                        .eq(Machine::getMachineNum, machineReturnDetail.getMachineNum()).one();
                if (!machine.getStatus().equals(MachineStatus.INVENTORY)&&machineReturn.getId() == null) {
                    throw new MaginaException("机器" + machine.getMachineNum() + "状态为" + machine.getStatus().getName() + "不允许退货！");
                }
                machineDomainService.lambdaUpdate()
                        .set(Machine::getStatus, MachineStatus.APPLY_RETURN)
                        .eq(Machine::getMachineNum, machineReturnDetail.getMachineNum()).update();
                machineReturnDetail.setTotalAmount(machine.getPurchasePrice());
                if (machineReturnDetail.getReturnType().equals(ReturnType.RETURN)) {
                    machineReturnDetail.setRefundAmount(0L);
                }
            }
        }
        Long totalAmount = machineReturnDetails.stream().mapToLong(MachineReturnDetail::getTotalAmount).sum();
        machineReturn.setTotalAmount(totalAmount);
        Long refundAmount = machineReturnDetails.stream().mapToLong(MachineReturnDetail::getRefundAmount).sum();
        machineReturn.setRefundAmount(refundAmount);
        machineReturnDetailDomainService.saveBatch(machineReturnDetails);
        return machineReturnDomainService.saveOrUpdate(machineReturn);
    }


    /**
     * 根据ID获取明细
     *
     * @param id
     * @return
     */
    public MachineReturn getById(Long id) {
        MachineReturn machineReturn =  machineReturnDomainService.selectJoinOne(MachineReturn.class, MPJWrappers.lambdaJoin()
                .selectAll(MachineReturn.class)
                .selectAs(Manufacturer::getName, MachineReturn::getManufacturerName)
                .leftJoin(Manufacturer.class, Manufacturer::getId, MachineReturn::getManufacturerId)
                .ge(MachineReturn::getId, id)
                .orderByDesc(Machine::getCreatedAt)
        );

        List<MachineReturnDetail> machineReturnDetails = machineReturnDetailDomainService.selectJoinList(MachineReturnDetail.class, MPJWrappers.lambdaJoin()
                .selectAll(MachineReturnDetail.class)
                .selectAs(Machine::getProductName, MachineReturnDetail::getProductName)
                .selectAs(Machine::getDeviceOn, MachineReturnDetail::getDeviceOn)
                .selectAs(Machine::getPercentage, MachineReturnDetail::getPercentage)
                .selectAs(Machine::getDeviceStatus, MachineReturnDetail::getDeviceStatus)
                .selectAs(Machine::getPurchasePrice, MachineReturnDetail::getPurchasePrice)
                .selectAs(Machine::getLocation, MachineReturnDetail::getLocation)
                .selectAs(Machine::getHostType, MachineReturnDetail::getHostType)
                .leftJoin(Machine.class, Machine::getMachineNum, MachineReturnDetail::getMachineNum)
                .eq(MachineReturnDetail::getReturnCode, machineReturn.getCode())
        );

        machineReturn.setMachineReturnDetails(machineReturnDetails);
        return machineReturn;
    }

    public Boolean audit(MachineRepairAuditDto machineRepairAuditDto) {
        MachineReturn machineReturn = machineReturnDomainService.getById(machineRepairAuditDto.getId());
        machineReturn.setRemark(machineRepairAuditDto.getRemark());
        List<MachineReturnDetail> machineReturnDetails = machineReturnDetailDomainService.lambdaQuery()
                .eq(MachineReturnDetail::getReturnCode, machineReturn.getCode())
                .list();
        if (machineRepairAuditDto.getIsPass()) {
            for (MachineReturnDetail machineReturnDetail : machineReturnDetails) {
                if (StringUtils.isNotBlank(machineReturnDetail.getMachineNum())) {
                    if(!machineReturnDetail.getReturnType().equals(ReturnType.REFUND)){
                        machineDomainService.lambdaUpdate()
                                .set(Machine::getStatus, MachineStatus.RETURN)
                                .eq(Machine::getMachineNum, machineReturnDetail.getMachineNum()).update();
                    }
                }
            }

            //更新采购明细退货数量
            Map<Long,List<MachineReturnDetail>> accessoryMap = machineReturnDetails.stream().collect(Collectors.groupingBy(MachineReturnDetail::getPurchaseDetailId));
            for (Map.Entry<Long, List<MachineReturnDetail>> entry : accessoryMap.entrySet()) {
                MachinePurchaseDetail machinePurchaseDetail = machinePurchaseDetailDomainService.getById(entry.getKey());
                if(machinePurchaseDetail.getCancelNum() == null){
                    machinePurchaseDetail.setCancelNum(0);
                }
                machinePurchaseDetail.setCancelNum(machinePurchaseDetail.getCancelNum() + entry.getValue().size());
                machinePurchaseDetailDomainService.updateById(machinePurchaseDetail);
            }
            machineReturn.setActureRefundAmount(machineReturn.getRefundAmount());
            machineReturn.setStatus(ReturnStatus.COMPLETE);
            MachinePurchase machinePurchase = machinePurchaseDomainService.lambdaQuery().eq(MachinePurchase::getPurchaseCode, machineReturn.getPurchaseCode()).one();
            machinePurchase.setRefundAmount(machinePurchase.getRefundAmount() + machineReturn.getRefundAmount());
            if (machinePurchase.getRefundAmount() > 0L) {
                //已付款
                if (machinePurchase.getPayStatus().equals(PayStatus.PAID)) {
                    if (machineReturn.getRefundType().equals(RefundTypeEnum.CASH)) {
                        machineReturn.setStatus(ReturnStatus.WAIT_REFUND);
                    } else {
                        Manufacturer manufacturer = manufacturerServiceDomain.getById(machinePurchase.getManufacturerId());
                        manufacturer.setFundBalance(manufacturer.getFundBalance() + machineReturn.getRefundAmount());
                        manufacturerServiceDomain.updateById(manufacturer);
                    }
                }
                if (machinePurchase.getPayStatus().equals(PayStatus.PART)) {
                    Long waitPayAmount = machinePurchase.getAmount() - machinePurchase.getPaidAmount();
                    //待付款大于退款金额 就少付  小于才退款
                    if (waitPayAmount < machineReturn.getRefundAmount()) {
                        Long refundAmount = machineReturn.getRefundAmount() - waitPayAmount;
                        machineReturn.setActureRefundAmount(refundAmount);
                        if (machineReturn.getRefundType().equals(RefundTypeEnum.CASH)) {
                            machineReturn.setStatus(ReturnStatus.WAIT_REFUND);
                        } else {
                            Manufacturer manufacturer = manufacturerServiceDomain.getById(machinePurchase.getManufacturerId());
                            manufacturer.setFundBalance(manufacturer.getFundBalance() + refundAmount);
                            manufacturerServiceDomain.updateById(manufacturer);
                        }
                    }
                }
            }
            machinePurchaseDomainService.updateById(machinePurchase);

        } else {
            machineReturn.setStatus(ReturnStatus.REJECT);
        }
        machineReturn.setAuditBy(ApplicationSessions.id());
        machineReturn.setAuditAt(LocalDateTime.now());
        return machineReturnDomainService.updateById(machineReturn);
    }

    public Boolean close(Long id) {
        MachineReturn machineReturn = machineReturnDomainService.getById(id);
        if (machineReturn.getStatus().equals(ReturnStatus.WAIT_APPROVE)
                || machineReturn.getStatus().equals(ReturnStatus.REJECT)) {

        } else {
            throw new IllegalArgumentException("当前退货单状态无法关闭！");
        }
        machineReturn.setStatus(ReturnStatus.CLOSED);
        List<MachineReturnDetail> machineReturnDetails = machineReturnDetailDomainService.lambdaQuery()
                .eq(MachineReturnDetail::getReturnCode, machineReturn.getCode())
                .list();
        for (MachineReturnDetail machineReturnDetail : machineReturnDetails) {
            if (StringUtils.isNotBlank(machineReturnDetail.getMachineNum())) {
                machineDomainService.lambdaUpdate()
                        .set(Machine::getStatus, MachineStatus.INVENTORY)
                        .eq(Machine::getMachineNum, machineReturnDetail.getMachineNum()).update();
            }
        }
        return machineReturnDomainService.updateById(machineReturn);
    }

    public Boolean refund(Long id) {
        MachineReturn machineReturn = machineReturnDomainService.getById(id);
        if (!machineReturn.getStatus().equals(ReturnStatus.WAIT_REFUND)) {
            throw new MaginaException("当前退货单状态无法完成退款！");
        }
        machineReturn.setStatus(ReturnStatus.COMPLETE);
        return machineReturnDomainService.updateById(machineReturn);
    }
}
