package com.hightop.benyin.machine.application.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.toolkit.MPJWrappers;
import com.google.common.collect.Lists;
import com.hightop.benyin.iot.infrastructure.entity.IotConfigure;
import com.hightop.benyin.item.domain.service.ItemServiceDomain;
import com.hightop.benyin.item.infrastructure.entity.Item;
import com.hightop.benyin.items.store.domain.service.ItemStoreLogServiceDomain;
import com.hightop.benyin.items.store.domain.service.ItemStoreServiceDomain;
import com.hightop.benyin.items.store.infrastructure.entity.ItemStore;
import com.hightop.benyin.items.store.infrastructure.enums.OperateTypeEnum;
import com.hightop.benyin.machine.api.dto.query.ComponentRepairPageQuery;
import com.hightop.benyin.machine.api.dto.vo.ComponentRepairAddDto;
import com.hightop.benyin.machine.api.dto.vo.ComponentRepairAuditDto;
import com.hightop.benyin.machine.api.dto.vo.ComponentRepairDto;
import com.hightop.benyin.machine.domain.service.ComponentRepairDomainService;
import com.hightop.benyin.machine.domain.service.ComponentRepairReplaceDomainService;
import com.hightop.benyin.machine.infrastructure.entity.ComponentRepair;
import com.hightop.benyin.machine.infrastructure.entity.ComponentRepairReplace;
import com.hightop.benyin.machine.infrastructure.entity.Machine;
import com.hightop.benyin.machine.infrastructure.entity.MachineDisassemble;
import com.hightop.benyin.machine.infrastructure.enums.ComponentRepairStatus;
import com.hightop.benyin.share.domain.service.SequenceDomainService;
import com.hightop.benyin.storage.domain.service.*;
import com.hightop.benyin.storage.infrastructure.entity.*;
import com.hightop.benyin.storage.infrastructure.enums.InOutTypeEnum;
import com.hightop.benyin.storage.infrastructure.enums.InStatusEnum;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.fario.common.mybatis.PageQuery;
import com.hightop.fario.common.mybatis.util.PageHelper;
import com.hightop.magina.casual.session.ApplicationSessions;
import com.hightop.magina.core.exception.MaginaException;
import com.hightop.magina.standard.ums.user.basic.UserBasic;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 组件维修服务
 *
 * <AUTHOR>
 * @date 2024-01-05 11:33:45
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
public class ComponentRepairService {

    ComponentRepairDomainService componentRepairDomainService;

    ComponentRepairReplaceDomainService componentRepairReplaceDomainService;
    ItemStoreLogServiceDomain itemStoreLogServiceDomain;
    StorageArticleServiceDomain storageArticleServiceDomain;
    ItemStoreServiceDomain itemStoreServiceDomain;
    ItemServiceDomain itemServiceDomain;
    SequenceDomainService sequenceDomainService;
    StorageInventoryBatchServiceDomain storageInventoryBatchServiceDomain;
    StorageInWarehouseServiceDomain storageInWarehouseServiceDomain;
    StorageInWarehouseGoodsServiceDomain storageInWarehouseGoodsServiceDomain;
    StorageInventoryServiceDomain storageInventoryServiceDomain;

    /**
     * 维修单列表
     *
     * @param pageQuery {@link PageQuery}
     * @return {@link DataGrid}
     */
    public DataGrid<ComponentRepair> minePage(ComponentRepairPageQuery pageQuery) {
        return PageHelper.startPage(pageQuery, p ->
                this.componentRepairDomainService.selectJoinList(ComponentRepair.class,
                        MPJWrappers.lambdaJoin()
                                .selectAll(ComponentRepair.class)
                                .selectAs(StorageArticle::getName, ComponentRepair::getArticleName)
                                .selectAs(StorageArticle::getType, ComponentRepair::getType)
                                .selectAs(StorageArticle::getImageFiles, ComponentRepair::getImageFiles)
                                .selectAs(StorageArticle::getManufacturerChannel, ComponentRepair::getManufacturerChannel)
                                .selectAs(StorageArticle::getNumberOem, ComponentRepair::getNumberOem)
                                .selectAs(UserBasic::getName, ComponentRepair::getEngineerName)
                                .leftJoin(StorageArticle.class, StorageArticle::getCode, ComponentRepair::getArticleCode)
                                .leftJoin(UserBasic.class, UserBasic::getId, ComponentRepair::getEngineerId)
                                .eq(pageQuery.getEngineerId() != null, ComponentRepair::getEngineerId, pageQuery.getEngineerId())
                                .like(StringUtils.isNotBlank(pageQuery.getNumberOem()), StorageArticle::getNumberOem, pageQuery.getNumberOem())
                                .like(StringUtils.isNotBlank(pageQuery.getArticleCode()), StorageArticle::getCode, pageQuery.getArticleCode())
                                .like(StringUtils.isNotBlank(pageQuery.getArticleName()), StorageArticle::getName, pageQuery.getArticleName())
                                .like(StringUtils.isNotBlank(pageQuery.getOriginCode()), Machine::getOriginCode, pageQuery.getOriginCode())
                                .like(StringUtils.isNotBlank(pageQuery.getCode()), ComponentRepair::getCode, pageQuery.getCode())
                                .like(StringUtils.isNotBlank(pageQuery.getEngineerName()), UserBasic::getName, pageQuery.getEngineerName())
                                .like(StringUtils.isNotBlank(pageQuery.getAuditName()), ComponentRepair::getAuditName, pageQuery.getAuditName())
                                .eq(StringUtils.isNotBlank(pageQuery.getStatus()), MachineDisassemble::getStatus, pageQuery.getStatus())
                                .ge(StringUtils.isNotBlank(pageQuery.getStartDate()), IotConfigure::getCreatedAt, pageQuery.getStartDate() + " 00:00:00")
                                .le(StringUtils.isNotBlank(pageQuery.getEndDate()), IotConfigure::getCreatedAt, pageQuery.getEndDate() + " 23:59:59")
                                .orderByDesc(ComponentRepair::getCreatedAt))
        );
    }

    public ComponentRepair getById(Long id) {
        ComponentRepair componentRepair = this.componentRepairDomainService.selectJoinOne(ComponentRepair.class,
                MPJWrappers.lambdaJoin()
                        .selectAll(ComponentRepair.class)
                        .selectAs(StorageArticle::getName, ComponentRepair::getArticleName)
                        .selectAs(StorageArticle::getType, ComponentRepair::getType)
                        .selectAs(StorageArticle::getImageFiles, ComponentRepair::getImageFiles)
                        .selectAs(StorageArticle::getManufacturerChannel, ComponentRepair::getManufacturerChannel)
                        .selectAs(UserBasic::getName, ComponentRepair::getEngineerName)
                        .leftJoin(StorageArticle.class, StorageArticle::getCode, ComponentRepair::getArticleCode)
                        .leftJoin(UserBasic.class, UserBasic::getId, ComponentRepair::getEngineerId)
                        .eq(ComponentRepair::getId, id));
        List<ComponentRepairReplace> componentRepairReplaces = componentRepairReplaceDomainService.selectJoinList(ComponentRepairReplace.class, MPJWrappers.lambdaJoin()
                .selectAll(ComponentRepairReplace.class)
                .selectAs(ItemStore::getOemNumber, ComponentRepairReplace::getOemNumber)
                .selectAs(ItemStore::getBatchCode, ComponentRepairReplace::getBatchCode)
                .selectAs(StorageArticle::getCode, ComponentRepairReplace::getArticleCode)
                .selectAs(StorageArticle::getName, ComponentRepairReplace::getArticleName)
                .selectAs(StorageArticle::getUnit, ComponentRepairReplace::getUnit)
                .selectAs(StorageArticle::getManufacturerChannel, ComponentRepairReplace::getManufacturerChannel)
                .leftJoin(ItemStore.class, ItemStore::getId, ComponentRepairReplace::getItemStoreId)
                .leftJoin(StorageArticle.class, StorageArticle::getCode, ItemStore::getArticleCode)
                .eq(ComponentRepairReplace::getRepairId, id)
        );
        if (CollectionUtils.isNotEmpty(componentRepairReplaces)) {
            for (ComponentRepairReplace componentRepairReplace : componentRepairReplaces) {
                ItemStore itemStore = itemStoreServiceDomain.getById(componentRepairReplace.getItemStoreId());

                StorageInventoryBatch storageInventoryBatch = storageInventoryBatchServiceDomain.lambdaQuery()
                        .eq(StorageInventoryBatch::getCode, itemStore.getArticleCode())
                        .eq(StorageInventoryBatch::getBatchCode, componentRepairReplace.getBatchCode()).one();
                if (storageInventoryBatch != null) {
                    componentRepairReplace.setCostPrice(storageInventoryBatch.getPrice());
                }
            }
        }
        componentRepair.setComponentRepairReplaces(componentRepairReplaces);
        return componentRepair;
    }

    public ComponentRepair getByCode(String code) {
        ComponentRepair componentRepair = this.componentRepairDomainService.selectJoinOne(ComponentRepair.class,
                MPJWrappers.lambdaJoin()
                        .selectAll(ComponentRepair.class)
                        .selectAs(StorageArticle::getName, ComponentRepair::getArticleName)
                        .selectAs(StorageArticle::getType, ComponentRepair::getType)
                        .selectAs(StorageArticle::getImageFiles, ComponentRepair::getImageFiles)
                        .selectAs(StorageArticle::getManufacturerChannel, ComponentRepair::getManufacturerChannel)
                        .selectAs(UserBasic::getName, ComponentRepair::getEngineerName)
                        .leftJoin(StorageArticle.class, StorageArticle::getCode, ComponentRepair::getArticleCode)
                        .leftJoin(UserBasic.class, UserBasic::getId, ComponentRepair::getEngineerId)
                        .eq(ComponentRepair::getCode, code));
        List<ComponentRepairReplace> componentRepairReplaces = componentRepairReplaceDomainService.selectJoinList(ComponentRepairReplace.class, MPJWrappers.lambdaJoin()
                .selectAll(ComponentRepairReplace.class)
                .selectAs(ItemStore::getOemNumber, ComponentRepairReplace::getOemNumber)
                .selectAs(ItemStore::getBatchCode, ComponentRepairReplace::getBatchCode)
                .selectAs(StorageArticle::getCode, ComponentRepairReplace::getArticleCode)
                .selectAs(StorageArticle::getName, ComponentRepairReplace::getArticleName)
                .selectAs(StorageArticle::getUnit, ComponentRepairReplace::getUnit)
                .selectAs(StorageArticle::getManufacturerChannel, ComponentRepairReplace::getManufacturerChannel)
                .leftJoin(ItemStore.class, ItemStore::getId, ComponentRepairReplace::getItemStoreId)
                .leftJoin(StorageArticle.class, StorageArticle::getCode, ItemStore::getArticleCode)
                .eq(ComponentRepairReplace::getRepairId, componentRepair.getId())
        );
        if (CollectionUtils.isNotEmpty(componentRepairReplaces)) {
            for (ComponentRepairReplace componentRepairReplace : componentRepairReplaces) {
                ItemStore itemStore = itemStoreServiceDomain.getById(componentRepairReplace.getItemStoreId());

                StorageInventoryBatch storageInventoryBatch = storageInventoryBatchServiceDomain.lambdaQuery()
                        .eq(StorageInventoryBatch::getCode, itemStore.getArticleCode())
                        .eq(StorageInventoryBatch::getBatchCode, componentRepairReplace.getBatchCode()).one();
                if (storageInventoryBatch != null) {
                    componentRepairReplace.setCostPrice(storageInventoryBatch.getPrice());
                }
            }
        }
        componentRepair.setComponentRepairReplaces(componentRepairReplaces);
        return componentRepair;
    }

    /**
     * 添加维修单
     *
     * @param componentRepairAddDto
     * @return
     */
    public Boolean repairMachine(ComponentRepairAddDto componentRepairAddDto) {
        StorageArticle storageArticle = storageArticleServiceDomain.lambdaQuery()
                .eq(StorageArticle::getCode, componentRepairAddDto.getArticleCode()).
                one();
        if (storageArticle == null) {
            throw new MaginaException("物品不存在!");
        }
        if (storageArticle.getArticleType().equals(1)) {
            throw new MaginaException("组合物品不能维修！");
        }

        ComponentRepair componentRepair = new ComponentRepair();
        componentRepair.setCode(sequenceDomainService.nextDateSequence("CR", 4));
        componentRepair.setArticleCode(storageArticle.getCode());
        componentRepair.setPartId(storageArticle.getPartId());
        componentRepair.setStatus(ComponentRepairStatus.REPAIRING);
        componentRepair.setEngineerId(ApplicationSessions.id());
        return componentRepairDomainService.save(componentRepair);
    }

    /**
     * 提交维修报告
     *
     * @param componentRepairDto
     * @return
     */
    public Boolean reportCommit(ComponentRepairDto componentRepairDto) {
        ComponentRepair componentRepair = componentRepairDomainService.getById(componentRepairDto.getId());

        if (componentRepairDto.getIsCommit()) {
            componentRepair.setCompletedAt(LocalDateTime.now());
            componentRepair.setStatus(ComponentRepairStatus.CONFIRM_REPORT);
        }
        componentRepair.setDescription(componentRepairDto.getDescription());
        componentRepair.setPicUrls(componentRepairDto.getPicUrls());
        componentRepair.setIsStore(componentRepairDto.getIsStore());
        List<ComponentRepairReplace> componentRepairReplaces = Lists.newArrayList();

        componentRepairReplaceDomainService.remove(Wrappers.<ComponentRepairReplace>lambdaQuery()
                .eq(ComponentRepairReplace::getRepairId, componentRepairDto.getId()));

        if (CollectionUtils.isNotEmpty(componentRepairDto.getComponentRepairReplaces())) {
            for (ComponentRepairReplace componentRepairReplace : componentRepairDto.getComponentRepairReplaces()) {
                componentRepairReplace.setId(null);
                componentRepairReplace.setRepairId(componentRepair.getId());
                ItemStore itemStore = itemStoreServiceDomain.getById(componentRepairReplace.getItemStoreId());

                //价格
                componentRepairReplace.setSaleUnitPrice(itemStore.getSaleUnitPrice());
                componentRepairReplace.setAmount(itemStore.getSaleUnitPrice() * componentRepairReplace.getNum());
                //批次
                componentRepairReplace.setBatchCode(itemStore.getBatchCode());

                Item item = itemServiceDomain.getById(itemStore.getItemId());
                componentRepairReplace.setItemStoreId(itemStore.getId());
                componentRepairReplace.setItemId(itemStore.getItemId());
                componentRepairReplace.setItemName(itemStore.getItemName());
                componentRepairReplace.setSaleSkuId(itemStore.getSaleSkuId());

                // 根据选择的位置
                componentRepairReplace.setSkuInfo(itemStore.getSkuInfo());
                componentRepairReplace.setIsPm(itemStore.getIsPm());
                componentRepairReplace.setPartId(itemStore.getPartId());
                componentRepairReplaces.add(componentRepairReplace);
                // 维修报告确认后，需要扣减所消耗的库存
                if (Objects.isNull(componentRepairReplace.getNum())) {
                    throw new MaginaException("未填写数量", item.getName());
                }

                itemStore.setLockNum(itemStore.getLockNum() + componentRepairReplace.getNum());
                itemStore.setAfterLockNum(itemStore.getAfterLockNum() - componentRepairReplace.getNum());
                if (itemStore.getNum() < 0 || itemStore.getAfterLockNum() < 0) {
                    throw new MaginaException("使用数量不能大于最大库存");
                }
                itemStore.setPackagePrice(itemStore.getAfterLockNum() == 0 ? null : itemStore.getPackagePrice());
                if (componentRepairDto.getIsCommit()) {
                    itemStoreServiceDomain.updateById(itemStore);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(componentRepairReplaces)) {
            componentRepair.setAmount(componentRepairReplaces.stream().mapToLong(ComponentRepairReplace::getAmount).sum());
            componentRepairReplaceDomainService.saveBatch(componentRepairReplaces);
        }
        return componentRepairDomainService.updateById(componentRepair);
    }

    /**
     * 维修确认
     *
     * @param componentRepairAuditDto
     * @return
     */
    public Boolean confirm(ComponentRepairAuditDto componentRepairAuditDto) {
        ComponentRepair componentRepair = componentRepairDomainService.getById(componentRepairAuditDto.getId());
        if (componentRepair == null) {
            throw new MaginaException("维修单不存在!");
        }
        List<ComponentRepairReplace> componentRepairReplaces = componentRepairReplaceDomainService.lambdaQuery()
                .eq(ComponentRepairReplace::getRepairId, componentRepairAuditDto.getId())
                .list();
        componentRepair.setAuditBy(ApplicationSessions.id());
        componentRepair.setAuditName(ApplicationSessions.name());
        componentRepair.setAuditAt(LocalDateTime.now());
        componentRepair.setIsStore(componentRepairAuditDto.getIsStore());
        componentRepair.setAmount(componentRepairAuditDto.getAmount());
        if (componentRepairAuditDto.getIsPass()) {
            componentRepair.setStatus(ComponentRepairStatus.COMPLETED);
            if (CollectionUtils.isNotEmpty(componentRepairReplaces)) {
                for (ComponentRepairReplace componentRepairReplace : componentRepairReplaces) {
                    ItemStore itemStore = itemStoreServiceDomain.getById(componentRepairReplace.getItemStoreId());
                    Integer alterBefore = itemStore.getNum();
                    itemStore.setNum(itemStore.getNum() - componentRepairReplace.getNum());
                    itemStore.setLockNum(itemStore.getLockNum() - componentRepairReplace.getNum());
                    itemStoreServiceDomain.updateById(itemStore);
                    // 记录库存变动日志
                    itemStoreLogServiceDomain.doSaveItemStoreLog(itemStore, componentRepair.getCode(), componentRepairReplace.getNum(), OperateTypeEnum.COMPONENT, itemStore.getBatchCode(), alterBefore, itemStore.getNum(),null);
                }
            }
            // 维修完成后，生成入库单
            if (componentRepair.getIsStore() != null && componentRepair.getIsStore()) {
                this.createStorageInWarehouse(componentRepair);
            }
        } else {
            componentRepair.setStatus(ComponentRepairStatus.REPAIRING);
            if (CollectionUtils.isNotEmpty(componentRepairReplaces)) {
                for (ComponentRepairReplace componentRepairReplace : componentRepairReplaces) {
                    ItemStore itemStore = itemStoreServiceDomain.getById(componentRepairReplace.getItemStoreId());
                    itemStore.setLockNum(itemStore.getLockNum() - componentRepairReplace.getNum());
                    itemStore.setAfterLockNum(itemStore.getAfterLockNum() + componentRepairReplace.getNum());
                    itemStoreServiceDomain.updateById(itemStore);
                }
            }
        }
        return componentRepairDomainService.updateById(componentRepair);
    }

    /**
     * 采购生成入库单
     *
     * @param componentRepair
     * @return
     */
    public Boolean createStorageInWarehouse(ComponentRepair componentRepair) {
        // ---------- 生成入库单 ----------
        String rkid = sequenceDomainService.nextDateSequence(StorageInWarehouse.SEQ_PREFIX, StorageInWarehouse.SEQ_LEN);
        StorageInventory inventory = storageInventoryServiceDomain.lambdaQuery()
                .eq(StorageInventory::getCode, componentRepair.getArticleCode())
                .last("LIMIT 1").one();
        if (inventory == null) {
            throw new MaginaException("库品不存在，请先添加" + componentRepair.getArticleCode());
        }
        inventory.setAveragePrice(componentRepair.getAmount());
        storageInventoryServiceDomain.updateById(inventory);
        //套装商品入明细
        StorageInWarehouseGoods storageInWarehouseGoods = build(componentRepair.getAmount(), rkid, inventory.getWarehouseId(), componentRepair.getArticleCode(), 1);
        storageInWarehouseGoods.setBatchCode(sequenceDomainService.nextDateSequence("", 3));
        storageInWarehouseGoodsServiceDomain.save(storageInWarehouseGoods);
        //入库单主表
        StorageInWarehouse inWarehouse = new StorageInWarehouse();
        inWarehouse.setInWarehouseId(rkid);
        inWarehouse.setWarehouseId(inventory.getWarehouseId());
        // 入库类型为维修入库
        inWarehouse.setInType(InOutTypeEnum.REPAIR);
        // 添加采购订单号
        inWarehouse.setShopWaybill(componentRepair.getCode());
        // 入库状态为待入库
        inWarehouse.setInStatus(InStatusEnum.DRK);
        inWarehouse.setInWarehouseNumber(1);
        inWarehouse.setAuditInWarehouseNumber(0);
        return storageInWarehouseServiceDomain.save(inWarehouse);
    }

    private StorageInWarehouseGoods build(Long price, String rkid, Long warehouseId, String articleCode, Integer num) {
        StorageInWarehouseGoods inGoods = new StorageInWarehouseGoods();
        inGoods.setInWarehouseId(rkid);
        LambdaQueryWrapper<StorageArticle> articleWrapper = new LambdaQueryWrapper<>();
        articleWrapper.eq(StorageArticle::getCode, articleCode);
        StorageArticle article = storageArticleServiceDomain.getOne(articleWrapper);
        inGoods.setCode(article.getCode());
        inGoods.setName(article.getName());
        inGoods.setWarehouseId(warehouseId);
        inGoods.setInWarehouseNumber(num);
        inGoods.setAuditInWarehouseNumber(0);
        inGoods.setCode(article.getCode());
        inGoods.setName(article.getName());
        inGoods.setPrice(price);
        return inGoods;
    }
}
