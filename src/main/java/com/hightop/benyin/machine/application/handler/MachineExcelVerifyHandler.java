package com.hightop.benyin.machine.application.handler;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.hightop.benyin.machine.api.dto.excel.MachineExcel;
import com.hightop.benyin.machine.domain.service.MachineDomainService;
import com.hightop.benyin.machine.infrastructure.entity.Machine;
import com.hightop.benyin.machine.infrastructure.enums.MachineStatus;
import com.hightop.benyin.product.domain.service.ProductAccessoryDomainService;
import com.hightop.benyin.product.domain.service.ProductTreeDomainService;
import com.hightop.benyin.product.infrastructure.entity.ProductAccessory;
import com.hightop.benyin.product.infrastructure.entity.ProductTree;
import com.hightop.benyin.share.application.service.DictExtendService;
import com.hightop.benyin.storage.domain.service.ManufacturerServiceDomain;
import com.hightop.benyin.storage.infrastructure.entity.Manufacturer;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.magina.core.component.ApplicationContexts;
import com.hightop.magina.standard.code.dictionary.item.DictItem;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.StringJoiner;

public class MachineExcelVerifyHandler implements IExcelVerifyHandler<MachineExcel> {
    @Override
    public ExcelVerifyHandlerResult verifyHandler(MachineExcel data) {

        MachineDomainService machineDomainService = ApplicationContexts.getBean(MachineDomainService.class);
        ProductTreeDomainService productTreeDomainService = ApplicationContexts.getBean(ProductTreeDomainService.class);
        DictExtendService dictExtendService = ApplicationContexts.getBean(DictExtendService.class);
        ProductAccessoryDomainService productAccessoryDomainService = ApplicationContexts.getBean(ProductAccessoryDomainService.class);
        ManufacturerServiceDomain manufacturerServiceDomain = ApplicationContexts.getBean(ManufacturerServiceDomain.class);

        Machine machine = machineDomainService.getMachineByCode(data.getOriginCode());
        StringJoiner joiner = new StringJoiner(",");

        if (machine != null) {
            data.setOriMachine(machine);
        }
        DictItem dictItem = dictExtendService.getByDictCodeAndLabel(Machine.HOST_TYPE, data.getHostType());
        if (Objects.isNull(dictItem)) {
            joiner.add("主机类型有误,参考数据字典" + Machine.HOST_TYPE);
        } else {
            data.setHostType(dictItem.getValue());

            if (data.getHostType().equals(Machine.HOST_TYPE_MACHINE)) {
                ProductTree productTree = productTreeDomainService.lambdaQuery()
                        .eq(ProductTree::getName, data.getProductName()).one();
                if(productTree == null){
                    joiner.add("机器型号名称有误,参数设备机型" );
                }else{
                    data.setProductId(productTree.getId());
                }
            }else{
                ProductAccessory productAccessory = productAccessoryDomainService.lambdaQuery()
                        .eq(ProductAccessory::getModeType, data.getProductName()).one();
                if(productAccessory == null){
                    joiner.add("机器型号名称有误,参考选配件型号" );
                }else{
                    data.setProductId(productAccessory.getId());
                }
            }
        }

        if(StringUtils.isNotBlank(data.getManufacturerCode())){
            Manufacturer manufacturer = manufacturerServiceDomain.lambdaQuery().eq(Manufacturer::getCode, data.getManufacturerCode()).one();
            if(manufacturer==null){
                joiner.add("制造商编号有误,参考制造商列表");
            }else{
                data.setManufacturerId(manufacturer.getId());
            }
        }

        if(StringUtils.isNotBlank(data.getDeviceOn())){
            DictItem deviceOn = dictExtendService.getByDictCodeAndLabel(Machine.DEVICE_ON, data.getDeviceOn());
            if (Objects.isNull(deviceOn)) {
                joiner.add("设备新旧有误,参考数据字典" + Machine.DEVICE_ON);
            } else {
                data.setDeviceOn(deviceOn.getValue());
            }
        }

        if(StringUtils.isNotBlank(data.getPercentage())){
            DictItem percentage = dictExtendService.getByDictCodeAndLabel(Machine.PERCENTAGE, data.getPercentage());
            if (Objects.isNull(percentage)) {
                joiner.add("设备成色有误,参考数据字典" + Machine.PERCENTAGE);
            } else {
                data.setDeviceOn(percentage.getValue());
            }
        }

        if(StringUtils.isNotBlank(data.getStatus())){
            MachineStatus status = MachineStatus.getEnum(data.getStatus());
            if (Objects.isNull(status)) {
                joiner.add("状态有误");
            } else {
                data.setMachineStatus(status);
            }
        }

        if(data.getPurchasePrice()!=null) {
            if(data.getPurchasePrice().compareTo(BigDecimal.ZERO)<0){
                joiner.add("采购单价不能为负数");
            }
        }
        String errorMsg = joiner.toString();
        if (StringUtils.isNotBlank(errorMsg)) {
            return new ExcelVerifyHandlerResult(false, errorMsg);
        }
        return new ExcelVerifyHandlerResult(true);
    }
}
