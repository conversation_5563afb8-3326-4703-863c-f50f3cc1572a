package com.hightop.benyin.machine.application.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.toolkit.MPJWrappers;
import com.google.common.collect.Lists;
import com.hightop.benyin.iot.infrastructure.entity.IotConfigure;
import com.hightop.benyin.item.domain.service.ItemServiceDomain;
import com.hightop.benyin.item.infrastructure.entity.Item;
import com.hightop.benyin.items.store.domain.service.ItemStoreLogServiceDomain;
import com.hightop.benyin.items.store.domain.service.ItemStoreServiceDomain;
import com.hightop.benyin.items.store.infrastructure.entity.ItemStore;
import com.hightop.benyin.items.store.infrastructure.enums.OperateTypeEnum;
import com.hightop.benyin.machine.api.dto.query.MachineRepairPageQuery;
import com.hightop.benyin.machine.api.dto.vo.MachineRepairAddDto;
import com.hightop.benyin.machine.api.dto.vo.MachineRepairAuditDto;
import com.hightop.benyin.machine.api.dto.vo.MachineRepairDto;
import com.hightop.benyin.machine.domain.service.MachineDomainService;
import com.hightop.benyin.machine.domain.service.MachineRepairDomainService;
import com.hightop.benyin.machine.domain.service.MachineRepairReplaceDomainService;
import com.hightop.benyin.machine.infrastructure.entity.Machine;
import com.hightop.benyin.machine.infrastructure.entity.MachineDisassemble;
import com.hightop.benyin.machine.infrastructure.entity.MachineRepair;
import com.hightop.benyin.machine.infrastructure.entity.MachineRepairReplace;
import com.hightop.benyin.machine.infrastructure.enums.MachineRepairStatus;
import com.hightop.benyin.machine.infrastructure.enums.MachineStatus;
import com.hightop.benyin.share.domain.service.SequenceDomainService;
import com.hightop.benyin.share.infrastructure.util.DownloadResponseUtil;
import com.hightop.benyin.storage.domain.service.StorageInventoryBatchServiceDomain;
import com.hightop.benyin.storage.infrastructure.entity.StorageArticle;
import com.hightop.benyin.storage.infrastructure.entity.StorageInventoryBatch;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.fario.common.mybatis.PageQuery;
import com.hightop.fario.common.mybatis.util.PageHelper;
import com.hightop.magina.casual.session.ApplicationSessions;
import com.hightop.magina.core.exception.MaginaException;
import com.hightop.magina.standard.ums.user.basic.UserBasic;
import com.hightop.magina.standard.ums.user.bind.UserEntry;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 申诉服务
 *
 * <AUTHOR>
 * @date 2024-01-05 11:33:45
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
public class MachineRepairService {

    MachineRepairDomainService machineRepairDomainService;

    MachineRepairReplaceDomainService machineRepairReplaceDomainService;
    ItemStoreLogServiceDomain itemStoreLogServiceDomain;
    MachineDomainService machineDomainService;
    ItemStoreServiceDomain itemStoreServiceDomain;
    ItemServiceDomain itemServiceDomain;
    SequenceDomainService sequenceDomainService;
    StorageInventoryBatchServiceDomain storageInventoryBatchServiceDomain;

    /**
     * 维修单列表
     *
     * @param pageQuery {@link PageQuery}
     * @return {@link DataGrid}
     */
    public DataGrid<MachineRepair> minePage(MachineRepairPageQuery pageQuery) {
        return PageHelper.startPage(pageQuery, p ->
                this.getList(pageQuery)
        ).peek(p -> {
           this.setMachineRepairReplaces(p);
        });
    }

    private List<MachineRepair> getList(MachineRepairPageQuery pageQuery){
       return this.machineRepairDomainService.selectJoinList(MachineRepair.class,
                MPJWrappers.lambdaJoin()
                        .selectAll(MachineRepair.class)
                        .selectAs(Machine::getProductName, MachineRepair::getProductName)
                        .selectAs(Machine::getPicsUrl, MachineRepair::getMachinePics)
                        .selectAs(Machine::getOriginCode, MachineRepair::getOriginCode)
                        .selectAs(Machine::getDeviceSequence, MachineRepair::getDeviceSequence)
                        .selectAs(Machine::getDeviceStatus, MachineRepair::getDeviceStatus)
                        .selectAs(UserBasic::getName, MachineRepair::getEngineerName)
                        .leftJoin(Machine.class, Machine::getMachineNum, MachineRepair::getMachineNum)
                        .leftJoin(UserBasic.class, UserBasic::getId, MachineRepair::getEngineerId)
                        .eq(pageQuery.getEngineerId() != null, MachineRepair::getEngineerId, pageQuery.getEngineerId())
                        .like(StringUtils.isNotBlank(pageQuery.getMachineNum()), MachineDisassemble::getMachineNum, pageQuery.getMachineNum())
                        .like(StringUtils.isNotBlank(pageQuery.getOriginCode()), Machine::getOriginCode, pageQuery.getOriginCode())
                        .like(StringUtils.isNotBlank(pageQuery.getCode()), MachineRepair::getCode, pageQuery.getCode())
                        .like(StringUtils.isNotBlank(pageQuery.getEngineerName()), UserBasic::getName, pageQuery.getEngineerName())
                        .like(StringUtils.isNotBlank(pageQuery.getAuditName()), MachineRepair::getAuditName, pageQuery.getAuditName())
                        .eq(StringUtils.isNotBlank(pageQuery.getStatus()), MachineDisassemble::getStatus, pageQuery.getStatus())
                        .in(CollectionUtils.isNotEmpty(pageQuery.getProductIds()), MachineDisassemble::getProductId, pageQuery.getProductIds())
                        .ge(StringUtils.isNotBlank(pageQuery.getStartDate()), IotConfigure::getCreatedAt, pageQuery.getStartDate() + " 00:00:00")
                        .le(StringUtils.isNotBlank(pageQuery.getEndDate()), IotConfigure::getCreatedAt, pageQuery.getEndDate() + " 23:59:59")
                        .orderByDesc(MachineRepair::getCreatedAt));
    }

    private void setMachineRepairReplaces(MachineRepair p) {
        List<MachineRepairReplace> machineRepairReplaces = machineRepairReplaceDomainService.lambdaQuery()
                .eq(MachineRepairReplace::getRepairId, p.getId())
                .list();
        if (CollectionUtils.isNotEmpty(machineRepairReplaces)) {
            for (MachineRepairReplace machineRepairReplace : machineRepairReplaces) {
                ItemStore itemStore = itemStoreServiceDomain.getById(machineRepairReplace.getItemStoreId());
                List<StorageInventoryBatch> storageInventoryBatchs = storageInventoryBatchServiceDomain.lambdaQuery()
                        .eq(StorageInventoryBatch::getCode, itemStore.getArticleCode())
                        .eq(StorageInventoryBatch::getBatchCode, machineRepairReplace.getBatchCode()).list();
                if (CollectionUtils.isNotEmpty(storageInventoryBatchs)) {
                    machineRepairReplace.setCostPrice(storageInventoryBatchs.get(0).getPrice());
                }
            }
            Long amount = machineRepairReplaces.stream().mapToLong(v -> {
                if(v.getCostPrice()==null){
                    return v.getSaleUnitPrice() * v.getNum();
                }
                return v.getCostPrice() * v.getNum();
            }).sum();
            p.setReplaceAmount(new BigDecimal(amount).divide(DownloadResponseUtil.HUNDRED, 2, BigDecimal.ROUND_HALF_UP));
        }
    }

    public Boolean downloadMachineRepairs(HttpServletResponse response, MachineRepairPageQuery pageQuery) {
        try {
            //查询数据
            List<MachineRepair> excelList =this.getList(pageQuery);
            excelList.forEach(v -> {
                this.setMachineRepairReplaces(v);
            });
            //页面下载设置
            DownloadResponseUtil.addDownLoadHeader(response, "毛机维修列表.xlsx");
            //将并添加信息，实体类结构，类的数据做成excel表格对象workbook
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(), MachineRepair.class, excelList);
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    public MachineRepair getById(Long id) {
        MachineRepair machineRepair = this.machineRepairDomainService.selectJoinOne(MachineRepair.class,
                MPJWrappers.lambdaJoin()
                        .selectAll(MachineRepair.class)
                        .selectAs(Machine::getProductName, MachineRepair::getProductName)
                        .selectAs(Machine::getPicsUrl, MachineRepair::getMachinePics)
                        .selectAs(UserBasic::getName, MachineRepair::getEngineerName)
                        .leftJoin(Machine.class, Machine::getMachineNum, MachineRepair::getMachineNum)
                        .leftJoin(UserBasic.class, UserBasic::getId, MachineRepair::getEngineerId)
                        .eq(MachineRepair::getId, id));
        List<MachineRepairReplace> machineRepairReplaces = machineRepairReplaceDomainService.selectJoinList(MachineRepairReplace.class, MPJWrappers.lambdaJoin()
                .selectAll(MachineRepairReplace.class)
                .selectAs(ItemStore::getOemNumber, MachineRepairReplace::getOemNumber)
                .selectAs(ItemStore::getBatchCode, MachineRepairReplace::getBatchCode)
                .selectAs(StorageArticle::getCode, MachineRepairReplace::getArticleCode)
                .selectAs(StorageArticle::getName, MachineRepairReplace::getArticleName)
                .selectAs(StorageArticle::getUnit, MachineRepairReplace::getUnit)
                .selectAs(StorageArticle::getManufacturerChannel, MachineRepairReplace::getManufacturerChannel)
                .leftJoin(ItemStore.class, ItemStore::getId, MachineRepairReplace::getItemStoreId)
                .leftJoin(StorageArticle.class, StorageArticle::getCode, ItemStore::getArticleCode)
                .eq(MachineRepairReplace::getRepairId, id)
        );
        if (CollectionUtils.isNotEmpty(machineRepairReplaces)) {
            for (MachineRepairReplace machineRepairReplace : machineRepairReplaces) {
                ItemStore itemStore = itemStoreServiceDomain.getById(machineRepairReplace.getItemStoreId());

                List<StorageInventoryBatch> storageInventoryBatchs = storageInventoryBatchServiceDomain.lambdaQuery()
                        .eq(StorageInventoryBatch::getCode, itemStore.getArticleCode())
                        .eq(StorageInventoryBatch::getBatchCode, machineRepairReplace.getBatchCode()).list();
                if (CollectionUtils.isNotEmpty(storageInventoryBatchs)) {
                    machineRepairReplace.setCostPrice(storageInventoryBatchs.get(0).getPrice());
                }
                machineRepairReplace.setTotalCostPrice(machineRepairReplace.getCostPrice() * machineRepairReplace.getNum());
            }
            Long amount = machineRepairReplaces.stream().mapToLong(v -> {
                if(v.getCostPrice()==null){
                    return v.getSaleUnitPrice() * v.getNum();
                }
                return v.getCostPrice() * v.getNum();
            }).sum();
            machineRepair.setReplaceAmount(new BigDecimal(amount).divide(DownloadResponseUtil.HUNDRED, 2, BigDecimal.ROUND_HALF_UP));
        }
        machineRepair.setMachineRepairReplaces(machineRepairReplaces);
        return machineRepair;
    }

    /**
     * 添加维修单
     *
     * @param machineRepairAddDto
     * @return
     */
    public Boolean repairMachine(MachineRepairAddDto machineRepairAddDto) {
        Machine machine = machineDomainService.getById(machineRepairAddDto.getId());
        if (machine == null) {
            throw new MaginaException("机器不存在!");
        }
        if (machine.getStatus().equals(MachineStatus.OVER_SALE)) {
            throw new MaginaException("当前机器已出售!");
        }
        if (machine.getStatus().equals(MachineStatus.REPAIR)) {
            throw new MaginaException("当前机器正在维修中!");
        }

        MachineRepair machineRepair = new MachineRepair();
        machineRepair.setCode(sequenceDomainService.nextDateSequence("MR", 4));
        machineRepair.setMachineNum(machine.getMachineNum());
        machineRepair.setProductId(machine.getProductId());
        machineRepair.setHostType(machine.getHostType());
        machineRepair.setStatus(MachineRepairStatus.REPAIRING);
        machineRepair.setEngineerId(ApplicationSessions.id());
        machineRepair.setLocation(machineRepairAddDto.getNewLocation());
        machineRepair.setOriLocation(machine.getLocation());
        machineRepairDomainService.save(machineRepair);

        if (StringUtils.isNotBlank(machineRepairAddDto.getNewLocation())) {
            machine.setLocation(machineRepairAddDto.getNewLocation());
        }
        machine.setStatus(MachineStatus.REPAIR);
        if (machine.getCreatedBy() == null) {
            machine.setCreatedBy(new UserEntry().setId(ApplicationSessions.id()));
        }
        return machineDomainService.updateById(machine);
    }

    /**
     * 提交维修报告
     *
     * @param machineRepairDto
     * @return
     */
    public Boolean reportCommit(MachineRepairDto machineRepairDto) {
        MachineRepair machineRepair = machineRepairDomainService.getById(machineRepairDto.getId());
        machineRepair.setPicUrls(machineRepairDto.getPicUrls());
        machineRepair.setDescription(machineRepairDto.getDescription());
        machineRepair.setBlackWhiteCounter(machineRepairDto.getBlackWhiteCounter());
        machineRepair.setColorCounter(machineRepairDto.getColorCounter());
        machineRepair.setFiveColourCounter(machineRepairDto.getFiveColourCounter());
        machineRepair.setDeviceSequence(machineRepairDto.getDeviceSequence());
        machineRepair.setOriLocation(machineRepairDto.getOriLocation());
        if (machineRepairDto.getIsCommit()) {
            machineRepair.setCompletedAt(LocalDateTime.now());
            machineRepair.setStatus(MachineRepairStatus.CONFIRM_REPORT);
        }
        List<MachineRepairReplace> machineRepairReplaces = Lists.newArrayList();

        machineRepairReplaceDomainService.remove(Wrappers.<MachineRepairReplace>lambdaQuery()
                .eq(MachineRepairReplace::getRepairId, machineRepairDto.getId()));

        if (CollectionUtils.isNotEmpty(machineRepairDto.getMachineRepairReplaces())) {
            for (MachineRepairReplace machineRepairReplace : machineRepairDto.getMachineRepairReplaces()) {
                machineRepairReplace.setId(null);
                machineRepairReplace.setRepairId(machineRepair.getId());
                machineRepairReplace.setMachineNum(machineRepair.getMachineNum());
                ItemStore itemStore = itemStoreServiceDomain.getById(machineRepairReplace.getItemStoreId());

                //价格
                machineRepairReplace.setSaleUnitPrice(itemStore.getSaleUnitPrice());
                machineRepairReplace.setAmount(itemStore.getSaleUnitPrice() * machineRepairReplace.getNum());
                //批次
                machineRepairReplace.setBatchCode(itemStore.getBatchCode());

                Item item = itemServiceDomain.getById(itemStore.getItemId());
                machineRepairReplace.setItemStoreId(itemStore.getId());
                machineRepairReplace.setItemId(itemStore.getItemId());
                machineRepairReplace.setItemName(itemStore.getItemName());
                machineRepairReplace.setSaleSkuId(itemStore.getSaleSkuId());

                // 根据选择的位置
                machineRepairReplace.setSkuInfo(itemStore.getSkuInfo());
                machineRepairReplace.setIsPm(itemStore.getIsPm());
                machineRepairReplace.setPartId(itemStore.getPartId());
                machineRepairReplaces.add(machineRepairReplace);
                // 维修报告确认后，需要扣减所消耗的库存
                if (Objects.isNull(machineRepairReplace.getNum())) {
                    throw new MaginaException("未填写数量", item.getName());
                }

                itemStore.setLockNum(itemStore.getLockNum() + machineRepairReplace.getNum());
                itemStore.setAfterLockNum(itemStore.getAfterLockNum() - machineRepairReplace.getNum());
                if (itemStore.getNum() < 0 || itemStore.getAfterLockNum() < 0) {
                    throw new MaginaException("使用数量不能大于最大库存");
                }
                itemStore.setPackagePrice(itemStore.getAfterLockNum() == 0 ? null : itemStore.getPackagePrice());
                if (machineRepairDto.getIsCommit()) {
                    itemStoreServiceDomain.updateById(itemStore);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(machineRepairReplaces)) {
            machineRepair.setAmount(machineRepairReplaces.stream().mapToLong(MachineRepairReplace::getAmount).sum());
            machineRepairReplaceDomainService.saveBatch(machineRepairReplaces);
        }
        return machineRepairDomainService.updateById(machineRepair);
    }

    /**
     * 维修确认
     *
     * @param machineRepairAuditDto
     * @return
     */
    public Boolean confirm(MachineRepairAuditDto machineRepairAuditDto) {
        MachineRepair machineRepair = machineRepairDomainService.getById(machineRepairAuditDto.getId());
        if (machineRepair == null) {
            throw new MaginaException("维修单不存在!");
        }
        List<MachineRepairReplace> machineRepairReplaces = machineRepairReplaceDomainService.lambdaQuery()
                .eq(MachineRepairReplace::getRepairId, machineRepairAuditDto.getId())
                .list();
        machineRepair.setAuditBy(ApplicationSessions.id());
        machineRepair.setAuditName(ApplicationSessions.name());
        machineRepair.setAuditAt(LocalDateTime.now());
        machineRepair.setRemark(machineRepairAuditDto.getRemark());
        if (machineRepairAuditDto.getIsPass()) {
            if (CollectionUtils.isNotEmpty(machineRepairReplaces)) {
                for (MachineRepairReplace machineRepairReplace : machineRepairReplaces) {
                    ItemStore itemStore = itemStoreServiceDomain.getById(machineRepairReplace.getItemStoreId());
                    List<StorageInventoryBatch> storageInventoryBatchs = storageInventoryBatchServiceDomain.lambdaQuery()
                            .eq(StorageInventoryBatch::getCode, itemStore.getArticleCode())
                            .eq(StorageInventoryBatch::getBatchCode, machineRepairReplace.getBatchCode()).list();
                    if (CollectionUtils.isNotEmpty(storageInventoryBatchs)) {
                        machineRepairReplace.setCostPrice(storageInventoryBatchs.get(0).getPrice());
                    }
                }
            }
            Long amount = CollectionUtils.isNotEmpty(machineRepairReplaces) ?
                    machineRepairReplaces.stream().mapToLong(v -> {
                        return v.getCostPrice() * v.getNum();
                    }).sum()
                    : 0L;
            machineRepair.setStatus(MachineRepairStatus.COMPLETED);
            machineDomainService.lambdaUpdate()
                    .set(Machine::getStatus, MachineStatus.INVENTORY)
                    .set(Machine::getDeviceStatus, Machine.DEVICE_STATUS_OUTCOME)
                    .setSql(CollectionUtils.isNotEmpty(machineRepairReplaces), "purchase_price=purchase_price+" + amount)
                    .set(StringUtils.isNotBlank(machineRepair.getOriLocation()), Machine::getLocation, machineRepair.getOriLocation())
                    .eq(Machine::getMachineNum, machineRepair.getMachineNum())
                    .update();
            if (CollectionUtils.isNotEmpty(machineRepairReplaces)) {
                for (MachineRepairReplace machineRepairReplace : machineRepairReplaces) {
                    ItemStore itemStore = itemStoreServiceDomain.getById(machineRepairReplace.getItemStoreId());
                    Integer alterBefore = itemStore.getNum();
                    itemStore.setNum(itemStore.getNum() - machineRepairReplace.getNum());
                    itemStore.setLockNum(itemStore.getLockNum() - machineRepairReplace.getNum());
                    itemStoreServiceDomain.updateById(itemStore);
                    // 记录库存变动日志
                    itemStoreLogServiceDomain.doSaveItemStoreLog(itemStore, machineRepair.getCode(), machineRepairReplace.getNum(), OperateTypeEnum.MACHINE, itemStore.getBatchCode(), alterBefore, itemStore.getNum());
                }
            }
        } else {
            machineRepair.setStatus(MachineRepairStatus.REPAIRING);
            if (CollectionUtils.isNotEmpty(machineRepairReplaces)) {
                for (MachineRepairReplace machineRepairReplace : machineRepairReplaces) {
                    ItemStore itemStore = itemStoreServiceDomain.getById(machineRepairReplace.getItemStoreId());
                    itemStore.setLockNum(itemStore.getLockNum() - machineRepairReplace.getNum());
                    itemStore.setAfterLockNum(itemStore.getAfterLockNum() + machineRepairReplace.getNum());
                    itemStoreServiceDomain.updateById(itemStore);
                }
            }
        }
        return machineRepairDomainService.updateById(machineRepair);
    }

}
