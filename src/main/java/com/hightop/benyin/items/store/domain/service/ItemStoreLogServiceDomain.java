package com.hightop.benyin.items.store.domain.service;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.items.store.application.vo.ItemStoreCalibrateDto;
import com.hightop.benyin.items.store.infrastructure.entity.ItemStore;
import com.hightop.benyin.items.store.infrastructure.entity.ItemStoreLog;
import com.hightop.benyin.items.store.infrastructure.enums.OperateTypeEnum;
import com.hightop.benyin.items.store.infrastructure.mapper.ItemStoreLogMapper;
import com.hightop.fario.base.util.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/12/29 15:03
 */
@Service
public class ItemStoreLogServiceDomain extends MPJBaseServiceImpl<ItemStoreLogMapper, ItemStoreLog> {


    /**
     * 保存库存日志
     * @param itemStore
     * @param operateTypeEnum
     * @param alterBefore
     * @param alterEnd
     */
    public void doSaveItemStoreLog(ItemStore itemStore,String orderCode,Integer num, OperateTypeEnum operateTypeEnum,String batchCode,Integer alterBefore, Integer alterEnd,Long flowId) {
        ItemStoreLog itemStoreLog = new ItemStoreLog();
        itemStoreLog.setItemStoreId(itemStore.getId());
        itemStoreLog.setUserId(itemStore.getUserId());
        itemStoreLog.setUserType(itemStore.getUserType());
        itemStoreLog.setOperateType(operateTypeEnum);
        itemStoreLog.setOperateCode(orderCode);
        itemStoreLog.setNum(num);
        itemStoreLog.setAlterBefore(alterBefore);
        itemStoreLog.setBatchCode(batchCode);
        itemStoreLog.setAlterEnd(alterEnd);
        itemStoreLog.setFlowId(flowId);
        itemStoreLog.setCreatedAt(LocalDateTime.now());
        itemStoreLog.setUpdatedAt(LocalDateTime.now());
        this.save(itemStoreLog);
    }


    /**
     * 保存库存日志
     * @param itemStore
     * @param operateTypeEnum
     * @param alterBefore
     * @param alterEnd
     */
    public void doSaveItemStoreLog(ItemStore itemStore,String orderCode,Integer num, OperateTypeEnum operateTypeEnum,String batchCode,Integer alterBefore, Integer alterEnd) {
        ItemStoreLog itemStoreLog = new ItemStoreLog();
        itemStoreLog.setItemStoreId(itemStore.getId());
        itemStoreLog.setUserId(itemStore.getUserId());
        itemStoreLog.setUserType(itemStore.getUserType());
        itemStoreLog.setOperateType(operateTypeEnum);
        itemStoreLog.setOperateCode(orderCode);
        itemStoreLog.setNum(num);
        itemStoreLog.setAlterBefore(alterBefore);
        itemStoreLog.setBatchCode(batchCode);
        itemStoreLog.setAlterEnd(alterEnd);
        itemStoreLog.setCreatedAt(LocalDateTime.now());
        itemStoreLog.setUpdatedAt(LocalDateTime.now());
        this.save(itemStoreLog);
    }

    public List<ItemStoreCalibrateDto> selectApplyList(Long enggineerId){
        return baseMapper.selectApplyList(enggineerId);
    }

    public List<ItemStoreCalibrateDto> selectUsedList( Long enggineerId,String articleCode){
        return baseMapper.selectUsedList(enggineerId,articleCode);
    }

    public List<ItemStoreCalibrateDto> selectCustomerBuyList(Long enggineerId){
        return baseMapper.selectCustomerBuyList(enggineerId);
    }

    public List<ItemStoreCalibrateDto> selectCustomerUsedReturnList( Long enggineerId,String articleCode){
        return baseMapper.selectCustomerUsedReturnList(enggineerId,articleCode);
    }

}
