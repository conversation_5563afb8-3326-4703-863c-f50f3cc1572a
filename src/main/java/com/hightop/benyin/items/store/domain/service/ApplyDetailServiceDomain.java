package com.hightop.benyin.items.store.domain.service;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.items.store.infrastructure.entity.ApplyDetail;
import com.hightop.benyin.items.store.infrastructure.mapper.ApplyDetailMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/12/29 14:56
 */
@Service
public class ApplyDetailServiceDomain extends MPJBaseServiceImpl<ApplyDetailMapper, ApplyDetail> {
    /**
     * 根据领料单id查询领料单明细
     * @param applyOrderId 领料单id
     * @return {@link List}
     */
    public List<ApplyDetail> getByApplyId(Long applyOrderId) {
        return super.lambdaQuery().eq(ApplyDetail::getApplyOrderId, applyOrderId).list();
    }

    /**
     * 是否未完成领料
     * @param applyOrderId 领料单id
     * @return true/false
     */
    public boolean isUnfinished(Long applyOrderId) {
        return
            super.lambdaQuery()
                .eq(ApplyDetail::getApplyOrderId, applyOrderId)
                // 已收货数量小于领料数量则为未完成
                .ltSql(ApplyDetail::getReceivedNum, "num")
                .exists();
    }
}
