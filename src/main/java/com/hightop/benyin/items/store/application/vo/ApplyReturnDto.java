package com.hightop.benyin.items.store.application.vo;

import com.hightop.benyin.items.store.infrastructure.entity.ApplyReturnDetail;
import com.hightop.benyin.items.store.infrastructure.enums.ApplyReturnStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description: 退料申请
 * @Author: xhg
 * @Date: 2024/1/2 13:58
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ApplyReturnDto {

    @ApiModelProperty("领料明细id")
    @NotNull(message = "id不能为空")
    Long id;

    @ApiModelProperty("状态")
    @NotNull(message = "状态不能为空")
    ApplyReturnStatusEnum status;

    @ApiModelProperty("明细")
    @NotEmpty(message = "退料明细不能为空")
    private List<ApplyReturnDetail> applyReturnDetails;

}
