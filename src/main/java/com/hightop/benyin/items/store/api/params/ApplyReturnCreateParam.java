package com.hightop.benyin.items.store.api.params;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/8 14:42
 */
@ApiModel("耗材领用退回")
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ApplyReturnCreateParam {

    @ApiModelProperty("退料明细列表")
    List<Detail> detailList;

    @ApiModel("退料明细")
    @Data
    public static class Detail{
        @ApiModelProperty("耗材仓库ID")
        Long itemStoreId;

        @ApiModelProperty("退回数量")
        private Integer num ;
    }
}
