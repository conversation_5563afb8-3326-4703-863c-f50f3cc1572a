package com.hightop.benyin.items.store.domain.service;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.items.store.infrastructure.entity.ApplyReturnDetail;
import com.hightop.benyin.items.store.infrastructure.mapper.ApplyReturnDetailMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/12/29 14:56
 */
@Service
public class ApplyReturnDetailServiceDomain extends MPJBaseServiceImpl<ApplyReturnDetailMapper, ApplyReturnDetail> {
    /**
     * 通过领料单id获取领料单明细
     * @param applyOrderId 领料单id
     * @return {@link List}
     */
    public List<ApplyReturnDetail> getByApplyOrderId(Long applyOrderId) {
        return super.lambdaQuery().eq(ApplyReturnDetail::getApplyReturnId, applyOrderId).list();
    }
}
