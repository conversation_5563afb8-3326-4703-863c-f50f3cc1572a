package com.hightop.benyin.items.store.application.vo.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@ApiModel("客户耗材仓库导出")
public class ItemStoreCustomerExport {
    @Excel(name = "客户编码", width = 20, orderNum = "1")
    private String customerSeq;

    @Excel(name = "客户名称", width = 25, orderNum = "2")
    private String customerName;

    @Excel(name = "OEM编号", width = 20, orderNum = "3")
    private String oemNumber;

    @Excel(name = "物品编码", width = 20, orderNum = "4")
    private String articleCode;

    @Excel(name = "物品名称", width = 30, orderNum = "5")
    private String articleName;

    @Excel(name = "物品类型", width = 15, orderNum = "6")
    private String typeLabel;

    @Excel(name = "单位", width = 10, orderNum = "7")
    private String unit;

    @Excel(name = "销售单价", width = 15, orderNum = "8")
    private BigDecimal realSaleUnitPrice;

    @Excel(name = "数量", width = 10, orderNum = "9")
    private Integer num;

    @Excel(name = "金额", width = 15, orderNum = "10")
    private BigDecimal realAmount;
}