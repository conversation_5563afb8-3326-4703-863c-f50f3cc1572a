package com.hightop.benyin.items.store.application.vo;

import com.hightop.benyin.items.store.infrastructure.entity.ApplyReturn;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date 2024/1/17 10:34
 */
@Data
@ApiModel("退料单")
public class ApplyReturnVo extends ApplyReturn {

    @ApiModelProperty("工程师姓名")
    private String engineerName;

    @ApiModelProperty("工程师手机号")
    private String engineerMobile;

}
