package com.hightop.benyin.items.store.domain.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.item.domain.service.SaleSkuServiceDomain;
import com.hightop.benyin.items.store.infrastructure.entity.ApplyOrder;
import com.hightop.benyin.items.store.infrastructure.entity.BatchNum;
import com.hightop.benyin.items.store.infrastructure.entity.BatchPrice;
import com.hightop.benyin.items.store.infrastructure.entity.ItemStore;
import com.hightop.benyin.items.store.infrastructure.enums.OperateTypeEnum;
import com.hightop.benyin.items.store.infrastructure.enums.UserTypeEnum;
import com.hightop.benyin.items.store.infrastructure.mapper.ApplyOrderMapper;
import com.hightop.benyin.order.domain.service.TradeOrderDetailDomainService;
import com.hightop.benyin.order.domain.service.TradeOrderDomainService;
import com.hightop.benyin.order.infrastructure.enmu.TradeOrderStatusEnum;
import com.hightop.benyin.order.infrastructure.entity.TradeOrder;
import com.hightop.benyin.order.infrastructure.entity.TradeOrderDetail;
import com.hightop.benyin.product.domain.service.ProductPartBomDomainService;
import com.hightop.benyin.reverse.domain.service.ReverseOrderServiceDomain;
import com.hightop.benyin.reverse.infrastructure.entity.ReverseOrder;
import com.hightop.benyin.reverse.infrastructure.entity.ReverseOrderDetail;
import com.hightop.benyin.reverse.infrastructure.enums.ReverseProcessStatusEnum;
import com.hightop.benyin.storage.domain.service.StorageArticleServiceDomain;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.magina.core.exception.MaginaException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/12/29 14:51
 */
@Service
@Slf4j
public class ApplyOrderServiceDomain extends MPJBaseServiceImpl<ApplyOrderMapper, ApplyOrder> {

    @Autowired
    private ApplyDetailServiceDomain applyDetailServiceDomain;

    @Autowired
    private ItemStoreServiceDomain itemStoreServiceDomain;

    @Autowired
    private ItemStoreLogServiceDomain itemStoreLogServiceDomain;

    @Autowired
    private SaleSkuServiceDomain saleSkuServiceDomain;

    @Autowired
    private StorageArticleServiceDomain storageArticleServiceDomain;

    @Autowired
    private ProductPartBomDomainService productPartBomDomainService;

    @Autowired
    private TradeOrderDetailDomainService tradeOrderDetailDomainService;

    @Autowired
    private TradeOrderDomainService tradeOrderDomainService;

    @Autowired
    private ReverseOrderServiceDomain reverseOrderServiceDomain;

    /**
     * 根据领料单号查询领料单
     *
     * @param code 领料单号
     * @return {@link ApplyOrder}
     */
    public ApplyOrder getByCode(String code) {
        return super.lambdaQuery().eq(ApplyOrder::getCode, code).one();
    }


    public List<BatchNum> getBatchCodes(String orderNum, String articleCode) {
        return this.baseMapper.getBatchCodes(orderNum, articleCode);
    }


    /**
     * 售后申请，锁定库存
     *
     * @param tradeOrderDetailIdList
     * @param customerId
     */
    public void lockItemStoreNum(List<Long> tradeOrderDetailIdList,String orderNum, Long customerId, Integer reverseNum) {
        if (CollectionUtils.isEmpty(tradeOrderDetailIdList)) {
            return;
        }
        List<TradeOrderDetail> tradeOrderDetailList = tradeOrderDetailDomainService.list(new LambdaQueryWrapper<TradeOrderDetail>()
                .in(TradeOrderDetail::getId, tradeOrderDetailIdList)
        );
        for (TradeOrderDetail tradeOrderDetail : tradeOrderDetailList) {
            reverseNum = reverseNum == null? tradeOrderDetail.getItemNum() - tradeOrderDetail.getReverseNum() : reverseNum;
            List<BatchNum> batchCodes = tradeOrderDomainService.getBatchCodes(orderNum, tradeOrderDetail.getArticleCode());
           for(BatchNum batchNum:batchCodes) {
               if(reverseNum==0L){
                   break;
               }
                ItemStore itemStore = itemStoreServiceDomain.getOne(new LambdaQueryWrapper<ItemStore>()
                        .eq(ItemStore::getUserId, customerId)
                        .eq(ItemStore::getUserType, UserTypeEnum.CUSTOMER)
                        .eq(ItemStore::getArticleCode, tradeOrderDetail.getArticleCode())
                        .eq(ItemStore::getBatchCode, batchNum.getBatchCode())
                );
                if (itemStore == null) {
                    throw new MaginaException(tradeOrderDetail.getItemName()+"没有找到对应["+batchNum.getBatchCode()+"]批次的库存记录，请联系管理员！");
                }
                Integer lockNum = itemStore.getAfterLockNum() > reverseNum
                        ? reverseNum : itemStore.getAfterLockNum();
                // 锁定值累加
                itemStore.setLockNum(itemStore.getLockNum() + lockNum);
                // 被锁定后的值累减
                itemStore.setAfterLockNum(itemStore.getNum() - itemStore.getLockNum());   // 被锁定后的值累减
                itemStore.setNum(itemStore.getNum() - lockNum);
                itemStoreServiceDomain.updateById(itemStore);
               reverseNum=reverseNum-lockNum;
            }
        }
    }

    /**
     * 释放被售后锁定的库存
     *
     * @param reverseOrderId
     */
    public void releaseItemStoreNum(Long reverseOrderId, Long customerId) {
        ReverseOrder reverseOrder = reverseOrderServiceDomain.getFullByReverseOrderId(reverseOrderId);
        //没有确认过售后不会加入到耗材仓库，不用减库存
        TradeOrder tradeOrder = tradeOrderDomainService.getById(reverseOrder.getTradeOrderId());
        if (TradeOrderStatusEnum.SUCCESS.equals(tradeOrder.getOrderStatus())) {
            return;
        }
        List<ReverseOrderDetail> reverseOrderDetailList = reverseOrder.getReverseOrderDetailList();
        for (ReverseOrderDetail reverseOrderDetail : reverseOrderDetailList) {

            List<BatchNum> batchCodes = tradeOrderDomainService.getBatchCodes(reverseOrder.getTradeOrderNum(), reverseOrderDetail.getArticleCode());
            for(BatchNum batchNum:batchCodes) {
                ItemStore itemStore = itemStoreServiceDomain.getOne(new LambdaQueryWrapper<ItemStore>()
                        .eq(ItemStore::getUserId, customerId)
                        .eq(ItemStore::getUserType, UserTypeEnum.CUSTOMER)
                        .eq(ItemStore::getArticleCode, reverseOrderDetail.getArticleCode())
                        .eq(ItemStore::getBatchCode, batchNum.getBatchCode())
                );
                if (itemStore == null) {
                    throw new MaginaException(reverseOrderDetail.getItemName()+"没有找到对应["+batchNum.getBatchCode()+"]批次的库存记录，请联系管理员！");
                }
                itemStore.setLockNum(itemStore.getLockNum() - reverseOrderDetail.getItemNum());
                itemStore.setAfterLockNum(itemStore.getNum() + itemStore.getLockNum());
                itemStore.setNum(itemStore.getNum() + reverseOrderDetail.getItemNum());
                itemStoreServiceDomain.updateById(itemStore);
            }
        }
    }

    /**
     * 订单退款成功
     *
     * @param reverseOrderId
     * @return
     */
    public Boolean decItemStoreByReverseId(Long reverseOrderId, boolean isDelivery) {
        ReverseOrder reverseOrder = reverseOrderServiceDomain.getFullByReverseOrderId(reverseOrderId);
        TradeOrder tradeOrder = tradeOrderDomainService.getById(reverseOrder.getTradeOrderId());
//        if (!TradeOrderStatusEnum.SUCCESS.equals(tradeOrder.getOrderStatus())) {
//            log.info("没有确认收货，无需减耗材库存.{}", tradeOrder.getOrderNum());
//            return Boolean.TRUE;
//        }
        if (!ReverseProcessStatusEnum.SUCCESS.equals(reverseOrder.getProcessStatus())) {
            throw new MaginaException("无效的状态", reverseOrder.getCode());
        }
        List<ReverseOrderDetail> reverseOrderDetailList = reverseOrder.getReverseOrderDetailList();
        for (ReverseOrderDetail reverseOrderDetail : reverseOrderDetailList) {

            List<BatchNum> batchCodes = tradeOrderDomainService.getBatchCodes(tradeOrder.getOrderNum(), reverseOrderDetail.getArticleCode());
            batchCodes.forEach(batchNum -> {
                ItemStore itemStore = itemStoreServiceDomain.getOne(new LambdaQueryWrapper<ItemStore>()
                        .eq(ItemStore::getUserId, tradeOrder.getCustomerId())
                        .eq(ItemStore::getUserType, UserTypeEnum.CUSTOMER)
                        .eq(ItemStore::getArticleCode, reverseOrderDetail.getArticleCode())
                        .eq(ItemStore::getBatchCode, batchNum.getBatchCode())
                );
                if (itemStore == null) {
                    throw new MaginaException(reverseOrderDetail.getItemName()+"没有找到对应["+batchNum.getBatchCode()+"]批次的库存记录，请联系管理员！");
                }
                Integer decreaseNum = reverseOrderDetail.getReduceItemNum();
                if (decreaseNum <= 0) {
                    throw new MaginaException("退货数量不能为0！");
                }
                int afterBefore = itemStore.getNum()+decreaseNum;
                itemStore.setLockNum(itemStore.getLockNum() - decreaseNum);
                itemStore.setPackagePrice(itemStore.getAfterLockNum()==0?null:itemStore.getPackagePrice());
                itemStore.setBatchCode(itemStore.getAfterLockNum()==0?null:itemStore.getBatchCode());
                itemStoreServiceDomain.updateById(itemStore);

                // 记录库存变动日志
                itemStoreLogServiceDomain.doSaveItemStoreLog(itemStore,reverseOrder.getCode(),decreaseNum, OperateTypeEnum.REVERSE,batchNum.getBatchCode(), afterBefore,itemStore.getNum());
            });
        }
        return Boolean.TRUE;
    }

}
