package com.hightop.benyin.items.store.domain.service;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.items.store.infrastructure.entity.ApplyReturn;
import com.hightop.benyin.items.store.infrastructure.mapper.ApplyReturnMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2023/12/29 14:51
 */
@Service
public class ApplyReturnOrderServiceDomain extends MPJBaseServiceImpl<ApplyReturnMapper, ApplyReturn> {
    /**
     * 通过退料单号获取退料单
     * @param code 退料单号
     * @return {@link ApplyReturn}
     */
    public ApplyReturn getByCode(String code) {
        return this.lambdaQuery().eq(ApplyReturn::getCode, code).one();
    }
}
