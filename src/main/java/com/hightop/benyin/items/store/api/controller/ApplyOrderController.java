package com.hightop.benyin.items.store.api.controller;

import com.hightop.benyin.items.store.api.params.ApplyOrderCreateParam;
import com.hightop.benyin.items.store.api.params.ApplyReturnCreateParam;
import com.hightop.benyin.items.store.api.params.PcApplyPageQuery;
import com.hightop.benyin.items.store.application.service.ApplyOrderService;
import com.hightop.benyin.items.store.application.service.ApplyReturnService;
import com.hightop.benyin.items.store.application.vo.ApplyOrderVo;
import com.hightop.benyin.items.store.application.vo.ApplyReturnVo;
import com.hightop.benyin.items.store.application.vo.CancelApplyVo;
import com.hightop.benyin.items.store.infrastructure.entity.ApplyOrder;
import com.hightop.fario.base.web.Operation;
import com.hightop.fario.base.web.RestResponse;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.fario.common.mybatis.PageQuery;
import com.hightop.magina.casual.session.ApplicationSessions;
import com.hightop.magina.standard.behavior.operation.IgnoreOperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AccessLevel;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2024/1/8 14:49
 */
@RestController
@RequestMapping("/applyOrder")
@FieldDefaults(level = AccessLevel.PRIVATE)
@RequiredArgsConstructor
@Api(tags = "工程师-耗材领用")
@Data
public class ApplyOrderController {

    @Autowired
    private ApplyOrderService applyOrderService;

    @Autowired
    private ApplyReturnService applyReturnService;

    @PostMapping("/apply")
    @ApiOperation("领用耗材")
    public RestResponse<Void> apply(@RequestBody ApplyOrderCreateParam apply) {
        return Operation.ADD.response(applyOrderService.apply(apply, ApplicationSessions.id()));
    }

    @PostMapping("/cancelDetail")
    @ApiOperation("取消领用耗材明细")
    public RestResponse<Void> cancelApplyDetail(@Validated @RequestBody CancelApplyVo cancelApplyVo) {
        return Operation.UPDATE.response(applyOrderService.cancelApplyDetail(cancelApplyVo));
    }

    @PostMapping("/cancelOrder")
    @ApiOperation("取消领用耗材整单")
    public RestResponse<Void> cancelApplyOrder(@Validated @RequestBody CancelApplyVo cancelApplyVo) {
        return Operation.UPDATE.response(applyOrderService.cancelApplyOrder(cancelApplyVo));
    }

    @PostMapping("/returnItemStore")
    @ApiOperation("领用耗材退回仓库")
    public RestResponse<Void> returnItemStore(@RequestBody ApplyReturnCreateParam applyReturnCreateParam) {

        return Operation.ADD.response(
            applyOrderService.returnItemStore(applyReturnCreateParam, ApplicationSessions.id()));
    }

    @PostMapping("/returnItemStore/list")
    @ApiOperation("退回申请列表")
    @IgnoreOperationLog
    public RestResponse<DataGrid<ApplyReturnVo>> returnItemStoreList(@RequestBody PageQuery pageQuery) {

        return RestResponse.ok(applyReturnService.pageListByEngineerId(pageQuery, ApplicationSessions.id()));
    }

    /**
     * 工程师领料列表
     * @param pageQuery
     * @return
     */
    @PostMapping("/listPage")
    @ApiOperation("申请记录")
    @IgnoreOperationLog
    public RestResponse<DataGrid<ApplyOrder>> listPage(@RequestBody PageQuery pageQuery) {

        return RestResponse.ok(applyOrderService.listPage(pageQuery, ApplicationSessions.id()));
    }


    @PostMapping("/pcListPage")
    @ApiOperation("领料单分页查询")
    @IgnoreOperationLog
    public RestResponse<DataGrid<ApplyOrderVo>> pcListPage(@RequestBody PcApplyPageQuery pageQuery) {

        return RestResponse.ok(applyOrderService.pcPageList(pageQuery));
    }

    @GetMapping("/detail/{id}")
    @ApiOperation("详情")
    public RestResponse<ApplyOrderVo> detail(@PathVariable @ApiParam("id") Long id) {
        return RestResponse.ok(applyOrderService.getById(id));
    }
}
