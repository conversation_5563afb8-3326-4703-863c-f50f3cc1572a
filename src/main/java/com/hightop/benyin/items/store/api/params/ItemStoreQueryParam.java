package com.hightop.benyin.items.store.api.params;

import com.baomidou.mybatisplus.annotation.TableField;
import com.hightop.benyin.item.api.params.ShopTagQuery;
import com.hightop.benyin.items.store.infrastructure.enums.ItemStoreSkuSourceEnum;
import com.hightop.benyin.items.store.infrastructure.enums.UserTypeEnum;
import com.hightop.fario.common.mybatis.PageQuery;
import com.hightop.magina.standard.code.dictionary.bind.DictItemBind;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/6 11:17
 */
@ApiModel("耗材仓库查询")
@Data
public class ItemStoreQueryParam extends PageQuery {

    @ApiModelProperty(value = "分类属性")
    List<ShopTagQuery> tags;

    @ApiModelProperty("仓库id")
    private Long itemStoreId;

    @ApiModelProperty("商品分类")
    private Long categoryId;

    @ApiModelProperty("物品大小型")
    private String type;

    @ApiModelProperty("机型列表")
    public List<Long> productIdList;

    @ApiModelProperty("用户ID")
    public Long customerId;

    @ApiModelProperty("设备组ID")
    public Long deviceGroupId;

    @ApiModelProperty("oem编号")
    private String oem;

    @ApiModelProperty("商品名称")
    private String itemName;

    @ApiModelProperty("是否有库存")
    private Boolean hasInventory;

    @ApiModelProperty("仓库类型")
    private UserTypeEnum userType;

    @ApiModelProperty("商品来源")
    private ItemStoreSkuSourceEnum skuSource ;

    @ApiModelProperty("客户编码")
    private String customerSeq;

    @ApiModelProperty("客户名称")
    private String customerName;

    /**
     * 物品名称
     */
    @ApiModelProperty("物品名称")
    private String articleName;
    /**
     * 物品编码
     */
    @ApiModelProperty("物品编码")
    private String articleCode;

    /**
     * 用户名
     */
    @ApiModelProperty("用户名")
    private String userName ;


    @ApiModelProperty("领料库存量（大）")
    Integer maxNum;

    @ApiModelProperty("总库存量（小）")
    Integer minNum;

    @ApiModelProperty("来源类型多选")
    List<String> operateTypes;

    @ApiModelProperty("来源类型单选")
    String operateType;

    @ApiModelProperty("操作单号")
    private String operateCode ;

    @ApiModelProperty("签定时间-开始")
    String startDate;

    @ApiModelProperty("签定时间-结束")
    String endDate;
}
