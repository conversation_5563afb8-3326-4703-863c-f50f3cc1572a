package com.hightop.benyin.items.store.application.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.hightop.benyin.items.store.infrastructure.enums.ItemStoreSkuSourceEnum;
import com.hightop.benyin.items.store.infrastructure.enums.OperateTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;

/**
 * @Description: 工单分配
 * @Author: xhg
 * @Date: 2024/1/2 13:58
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ItemStoreCalibrateDto {

    @ApiModelProperty("领料明细id")
    Long applyDetailId;

    @ApiModelProperty("工程师id")
    Long userId;

    @ApiModelProperty("订单号")
    String orderCode;

    @ApiModelProperty("物品编码")
    String articleCode;

    @ApiModelProperty("数量")
    Integer number;

    @ApiModelProperty("操作时间")
    LocalDateTime operationTime;

    @ApiModelProperty("操作类型")
    OperateTypeEnum operateType;

    @ApiModelProperty("来源")
    private ItemStoreSkuSourceEnum skuSource;
}
