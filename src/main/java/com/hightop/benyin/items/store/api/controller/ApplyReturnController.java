package com.hightop.benyin.items.store.api.controller;

import com.hightop.benyin.items.store.api.params.ApplyReturnPageQuery;
import com.hightop.benyin.items.store.application.service.ApplyReturnService;
import com.hightop.benyin.items.store.application.vo.ApplyReturnDto;
import com.hightop.benyin.items.store.application.vo.ApplyReturnVo;
import com.hightop.fario.base.web.Operation;
import com.hightop.fario.base.web.RestResponse;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.magina.standard.behavior.operation.IgnoreOperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AccessLevel;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Date 2024/1/17 10:13
 */
@RestController
@RequestMapping("/applyReturn")
@FieldDefaults(level = AccessLevel.PRIVATE)
@RequiredArgsConstructor
@Api(tags = "退料单")
@Data
public class ApplyReturnController {

    @Autowired
    private ApplyReturnService applyReturnService;

    @PostMapping("/page")
    @ApiOperation("退料单分页")
    @IgnoreOperationLog
    public RestResponse<DataGrid<ApplyReturnVo>> page(@RequestBody ApplyReturnPageQuery pageQuery) {
        return RestResponse.ok(applyReturnService.pageList(pageQuery));
    }

    @GetMapping("/detail/{id}")
    @ApiOperation("详情")
    public RestResponse<ApplyReturnVo> detail(@PathVariable @ApiParam("id") Long id) {
        return RestResponse.ok(applyReturnService.getById(id));
    }

    @PutMapping("/approve")
    @ApiOperation("审核")
    public RestResponse<Void> approve(@RequestBody @Validated ApplyReturnDto applyReturnDto) {
        return  Operation.UPDATE.response(applyReturnService.approve(applyReturnDto));
    }


}
