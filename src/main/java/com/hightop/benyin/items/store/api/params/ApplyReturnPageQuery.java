package com.hightop.benyin.items.store.api.params;

import com.hightop.fario.common.mybatis.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/1/17 10:17
 */
@ApiModel("退料单分页")
@Data
public class ApplyReturnPageQuery extends PageQuery {
    @ApiModelProperty("工程师名称")
    private String engineerName;
    @ApiModelProperty("工程师手机号")
    private String engineerMobile;
    @ApiModelProperty("关键词")
    private String keyword;
    @ApiModelProperty("状态")
    private String status;

}
