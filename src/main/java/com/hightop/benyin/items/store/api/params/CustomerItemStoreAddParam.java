package com.hightop.benyin.items.store.api.params;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/27 20:45
 */
@Data
@ApiModel("客户耗材仓库等级")
public class CustomerItemStoreAddParam {

    @ApiModelProperty("skuId")
    @NotNull(message = "商品不能为空")
    Long skuId;

    @ApiModelProperty("等级数量")
    @NotNull(message = "数量不能为空")
    private Integer num ;


}
