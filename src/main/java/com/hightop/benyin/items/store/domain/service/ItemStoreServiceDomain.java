package com.hightop.benyin.items.store.domain.service;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.items.store.api.params.ItemStoreQueryParam;
import com.hightop.benyin.items.store.application.params.ItemStoreQuery;
import com.hightop.benyin.items.store.infrastructure.entity.ItemStore;
import com.hightop.benyin.items.store.infrastructure.enums.UserTypeEnum;
import com.hightop.benyin.items.store.infrastructure.mapper.ItemStoreMapper;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/12/29 14:40
 */
@Service
public class ItemStoreServiceDomain extends MPJBaseServiceImpl<ItemStoreMapper, ItemStore> {

    /**
     * 查询耗材仓库列表
     *
     * @param itemStoreQuery
     * @return
     */
    public List<ItemStore> queryList(ItemStoreQuery itemStoreQuery, List<List<String>> jsonTagList) {
        return this.baseMapper.queryList(itemStoreQuery, jsonTagList);
    }

    /**
     * 查询耗材仓库列表
     *
     * @param itemStoreQuery
     * @return
     */
    public List<ItemStore> pageList(ItemStoreQueryParam itemStoreQuery, List<List<String>> jsonTagList) {
        return this.baseMapper.pageList(itemStoreQuery, jsonTagList);
    }

    public BigDecimal total(ItemStoreQueryParam itemStoreQuery) {
        return this.baseMapper.total(itemStoreQuery);
    }

    /**
     * 获得指定sku库存
     *
     * @param articleCode  物品编码
     * @param userId       客户/工程师id
     * @param userTypeEnum 用户类型
     * @return {@link ItemStore}
     */
    protected ItemStore getStore(String articleCode, Long userId, Long skuId, UserTypeEnum userTypeEnum) {
        return
                super.lambdaQuery()
                        .eq(ItemStore::getArticleCode, articleCode)
                        .eq(ItemStore::getSaleSkuId, skuId)
                        .eq(ItemStore::getUserType, userTypeEnum.getValue())
                        .eq(ItemStore::getUserId, userId)
                        .one();
    }


    public void calibrateInventory() {
        List<ItemStore> itemStores = this.baseMapper.successOrder();

        // 部分发货
        List<ItemStore> partDeliverStores = this.baseMapper.partDeliver();
        Map<Long, ItemStore> partStoreMap = itemStores.stream().collect(Collectors.toMap(ItemStore::getId, Function.identity()));

        //工单使用
        List<ItemStore> workRepairStores = this.baseMapper.workRepairUsed();
        Map<Long, ItemStore> workStoreMap = workRepairStores.stream().collect(Collectors.toMap(ItemStore::getId, Function.identity()));

        //自修使用
        List<ItemStore> selfRepairStores = this.baseMapper.selfRepairUsed();
        Map<Long, ItemStore> selfStoreMap = workRepairStores.stream().collect(Collectors.toMap(ItemStore::getId, Function.identity()));
        // 退货
        List<ItemStore> reverseStores = this.baseMapper.reverse();
        Map<Long, ItemStore> reverseStoreMap = workRepairStores.stream().collect(Collectors.toMap(ItemStore::getId, Function.identity()));
        itemStores.forEach(itemStore -> {
            Integer num = itemStore.getNum();
            if (partStoreMap.containsKey(itemStore.getId())) {
                num = num + partStoreMap.get(itemStore.getId()).getNum();
            }
            if (workStoreMap.containsKey(itemStore.getId())) {
                num = num - workStoreMap.get(itemStore.getId()).getNum();
            }
            if (selfStoreMap.containsKey(itemStore.getId())) {
                num = num - selfStoreMap.get(itemStore.getId()).getNum();
            }
            if (reverseStoreMap.containsKey(itemStore.getId())) {
                num = num - reverseStoreMap.get(itemStore.getId()).getNum();
            }
            if (num < 0) {
                num = 0;
            }
            lambdaUpdate().set(ItemStore::getNum, num).eq(ItemStore::getId, itemStore.getId()).update();
        });
    }
}
