package com.hightop.benyin.activity.api;

import com.hightop.benyin.activity.application.service.ActivitySituationService;
import com.hightop.benyin.activity.application.vo.ActivitySituationOrderVo;
import com.hightop.benyin.activity.application.vo.ActivitySituationPageQuery;
import com.hightop.benyin.activity.application.vo.ActivitySituationSummaryVo;
import com.hightop.benyin.activity.application.vo.ActivitySituationVo;
import com.hightop.benyin.activity.infrastructure.entity.ActivitySituation;
import com.hightop.fario.base.web.RestResponse;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.magina.standard.behavior.operation.IgnoreOperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 活动情况接口
 *
 * <AUTHOR>
 * @date 2024-01-05 11:33:45
 */
@RequestMapping("/activity-situation")
@RestController
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Api(tags = "活动情况")
public class ActivitySituationController {

    ActivitySituationService activitySituationService;

    @PostMapping("/page")
    @ApiOperation("活动情况列表分页查询")
    @IgnoreOperationLog
    public RestResponse<DataGrid<ActivitySituation>> page(@RequestBody ActivitySituationPageQuery pageQuery) {
        return RestResponse.ok(this.activitySituationService.minePage(pageQuery));
    }

    @PostMapping("/summary")
    @ApiOperation("活动情况统计查询")
    @IgnoreOperationLog
    public RestResponse<ActivitySituationSummaryVo> summary(@RequestBody ActivitySituationPageQuery pageQuery) {
        return RestResponse.ok(this.activitySituationService.getActivitySituationSummary(pageQuery));
    }

    @PostMapping("/customer-page")
    @ApiOperation("活动分享客户入驻情况分页查询")
    @IgnoreOperationLog
    public RestResponse<DataGrid<ActivitySituationVo>> customerPage(@RequestBody ActivitySituationPageQuery pageQuery) {
        return RestResponse.ok(this.activitySituationService.activityCustomerPage(pageQuery));
    }

    @PostMapping("/customer-click")
    @ApiOperation("活动触达客户列表分页查询")
    @IgnoreOperationLog
    public RestResponse<DataGrid<ActivitySituationVo>> customerClickPage(@RequestBody ActivitySituationPageQuery pageQuery) {
        return RestResponse.ok(this.activitySituationService.activityCustomerSituationPageList(pageQuery));
    }

    @PostMapping("/order-customer")
    @ApiOperation("活动下单客户列表分页查询")
    @IgnoreOperationLog
    public RestResponse<DataGrid<ActivitySituationVo>> activitySituationOrderPage(@RequestBody ActivitySituationPageQuery pageQuery) {
        return RestResponse.ok(this.activitySituationService.activitySituationOrderCustList(pageQuery));
    }

    @PostMapping("/customer-order")
    @ApiOperation("活动分享客户下单情况分页查询")
    @IgnoreOperationLog
    public RestResponse<DataGrid<ActivitySituationOrderVo>> activityCustomeOrderPage(@RequestBody ActivitySituationPageQuery pageQuery) {
        return RestResponse.ok(this.activitySituationService.activityCustomeOrderPage(pageQuery));
    }


}
