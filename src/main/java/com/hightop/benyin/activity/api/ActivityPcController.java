package com.hightop.benyin.activity.api;

import com.hightop.benyin.activity.application.dto.ActivityAuditDto;
import com.hightop.benyin.activity.application.dto.ActivityGrantDto;
import com.hightop.benyin.activity.application.service.ActivityAwardGrantService;
import com.hightop.benyin.activity.application.service.ActivityService;
import com.hightop.benyin.activity.application.vo.ActivityAwardGrantSummaryVo;
import com.hightop.benyin.activity.application.vo.ActivityGrantPageQuery;
import com.hightop.benyin.activity.application.vo.ActivityGrantVo;
import com.hightop.benyin.activity.application.vo.ActivityPageQuery;
import com.hightop.benyin.activity.infrastructure.entity.Activity;
import com.hightop.benyin.activity.infrastructure.entity.ActivityAward;
import com.hightop.benyin.activity.infrastructure.entity.ActivityAwardGrant;
import com.hightop.fario.base.web.Operation;
import com.hightop.fario.base.web.RestResponse;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.magina.standard.behavior.operation.IgnoreOperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 活动PCrest接口
 *
 * <AUTHOR>
 * @date 2024-01-05 11:33:45
 */
@RequestMapping("/activity-pc")
@RestController
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Api(tags = "活动PC端")
public class ActivityPcController {

    ActivityService activityService;
    ActivityAwardGrantService activityAwardGrantService;

    @PostMapping("/page")
    @ApiOperation("活动列表分页查询")
    @IgnoreOperationLog
    public RestResponse<DataGrid<Activity>> page(@RequestBody ActivityPageQuery pageQuery) {
        return RestResponse.ok(this.activityService.minePage(pageQuery));
    }

    @PostMapping()
    @ApiOperation("活动新增修改")
    public RestResponse<Void> save(@Validated @RequestBody Activity activity) {
        return Operation.UPDATE.response(activityService.saveActivity(activity));
    }

    @GetMapping("/{id}")
    @ApiOperation("活动明细查询")
    public RestResponse<Activity> detail(@PathVariable("id") Long id) {
        return RestResponse.ok(this.activityService.detail(id));
    }

    @PutMapping("/audit")
    @ApiOperation("活动审核")
    public RestResponse<Void> audit(@Validated @RequestBody ActivityAuditDto activityAuditDto) {
        return Operation.UPDATE.response(activityService.auditActivity(activityAuditDto));
    }

    @GetMapping("/award/{id}")
    @ApiOperation("活动奖品查询")
    public RestResponse<List<ActivityAward>> getAwards(@PathVariable("id") Long id) {
        return RestResponse.ok(activityService.getAwards(id));
    }

    @PutMapping("/grant")
    @ApiOperation("活动奖品发放")
    public RestResponse<Void> grant(@Validated @RequestBody ActivityGrantDto activityGrantDto) {
        return Operation.UPDATE.response(activityService.grantAward(activityGrantDto));
    }

    @PostMapping("/grant-page")
    @ApiOperation("活动奖品发放分页查询")
    @IgnoreOperationLog
    public RestResponse<DataGrid<ActivityAwardGrant>> getAwardGrantPage(@RequestBody ActivityGrantPageQuery pageQuery) {
        return RestResponse.ok(this.activityAwardGrantService.getAwardGrantPage(pageQuery));
    }

    @PostMapping("/grant-type")
    @ApiOperation("活动奖品发放类型统计分页查询")
    @IgnoreOperationLog
    public RestResponse<DataGrid<ActivityGrantVo>> activityGrantPageList(@RequestBody ActivityGrantPageQuery pageQuery) {
        return RestResponse.ok(this.activityAwardGrantService.activityGrantPageList(pageQuery));
    }

    @PostMapping("/grant-summary")
    @ApiOperation("活动奖品发放统计")
    @IgnoreOperationLog
    public RestResponse<ActivityAwardGrantSummaryVo> getAwardGrantSummary(@RequestBody ActivityGrantPageQuery pageQuery) {
        return RestResponse.ok(this.activityAwardGrantService.getAwardGrantSummary(pageQuery));
    }

    @DeleteMapping("/{id}")
    @ApiOperation("活动删除")
    public RestResponse<Void> delete(@PathVariable("id") Long id) {
        return Operation.DELETE.response(activityService.deleteActivity(id));
    }
    @PutMapping("/stop/{id}")
    @ApiOperation("活动终止")
    public RestResponse<Void> stop(@PathVariable("id") Long id) {
        return Operation.UPDATE.response(activityService.stopActivity(id));
    }

    @PutMapping("/start/{id}")
    @ApiOperation("活动开始")
    public RestResponse<Void> start(@PathVariable("id") Long id) {
        return Operation.UPDATE.response(activityService.startActivity(id));
    }

}
