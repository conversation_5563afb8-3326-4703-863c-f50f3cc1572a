package com.hightop.benyin.activity.application.vo;


import com.hightop.benyin.activity.infrastructure.enums.ActivityType;
import com.hightop.fario.common.mybatis.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * 活动查询
 *
 * <AUTHOR>
 * @date 2024-01-05 16:28:37
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("活动查询O")
public class ActivityPageQuery extends PageQuery {

    @ApiModelProperty("活动编号")
    String code;

    @ApiModelProperty("活动名称")
    String activityName;

    @ApiModelProperty("活动类型")
    String activityType;

    @ApiModelProperty("状态(多选)")
    List<String> status;

    @ApiModelProperty("开始时间-起")
    String beginDateStart;

    @ApiModelProperty("开始时间-结束")
    String beginDateEnd;

    @ApiModelProperty("截止时间-起")
    String deadlineDateStart;

    @ApiModelProperty("截止时间-结束")
    String deadlineDateEnd;

    @ApiModelProperty("参与客户")
    Long customerId;

    @ApiModelProperty("区域范围")
    List<String> ranges;

    @ApiModelProperty("创建时间-起")
    String beginDate;

    @ApiModelProperty("创建时间-结束")
    String endDate;

}
