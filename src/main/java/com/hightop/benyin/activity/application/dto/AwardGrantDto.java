package com.hightop.benyin.activity.application.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.hightop.benyin.activity.infrastructure.entity.ActivityAward;
import com.hightop.benyin.activity.infrastructure.enums.AwardType;
import com.hightop.benyin.customer.infrastructure.enums.LimitTypeEnums;
import com.hightop.fario.common.jackson.annotation.JsonAmount;
import com.hightop.fario.common.mybatis.mysql.type.JsonTypeHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * @Description: 活动奖品发放明细DTO
 * @Author: xhg
 * @Date: 2024/1/5 16:00
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AwardGrantDto {

    @ApiModelProperty("奖品id")
    Long awardId;

    @ApiModelProperty("奖品类型")
    AwardType awardType;

    @ApiModelProperty("奖品名称")
    String awardName;

    @ApiModelProperty("费用预算")
    Long skuId;

    @ApiModelProperty("物品编码")
    String articleCode;

    @ApiModelProperty("单价")
    BigDecimal price;

    @ApiModelProperty("限制类型")
    LimitTypeEnums limitType;

    @ApiModelProperty("使用限制")
    List<Long> limitInfo;

    @ApiModelProperty("最低金额")
    @JsonAmount
    Long minAmount;

    @ApiModelProperty("过期时间")
    LocalDate expireDate;

    @ApiModelProperty("奖品数量")
    @NotNull(message="奖品数量不能为空")
    Integer quantity;


}
