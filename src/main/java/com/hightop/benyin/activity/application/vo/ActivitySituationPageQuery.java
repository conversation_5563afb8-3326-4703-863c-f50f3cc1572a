package com.hightop.benyin.activity.application.vo;


import com.hightop.fario.common.jackson.annotation.JsonAmount;
import com.hightop.fario.common.mybatis.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;

/**
 * 活动查询
 *
 * <AUTHOR>
 * @date 2024-01-05 16:28:37
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("活动查询O")
public class ActivitySituationPageQuery extends PageQuery {

    @ApiModelProperty("活动id")
    Long activityId;

    @ApiModelProperty("客户id")
    Long customerId;

    @ApiModelProperty("分享id")
    Long situationId;

    @ApiModelProperty("客户编码")
    String customerSeqId;

    @ApiModelProperty("客户名称")
    String customerName;

    @ApiModelProperty("活动名称")
    String activityName;

    @ApiModelProperty("是否发放奖品")
    Boolean isGrant;

    @ApiModelProperty("入驻时间-起")
    String startDate;

    @ApiModelProperty("入驻时间-止")
    String endDate;

    @ApiModelProperty("活动开始")
    LocalDateTime activityBeginTime;

    @ApiModelProperty("活动结束时间")
    LocalDateTime activityEndTime;

    @ApiModelProperty("所属省市区")
    String regionPath;

    @ApiModelProperty("消费金额-起")
    @JsonAmount
    Long startOrderAmount;

    @ApiModelProperty("消费金额-止")
    @JsonAmount
    Long endOrderAmount;


    @ApiModelProperty("订单数量-起")
    Integer startOrderNum;

    @ApiModelProperty("订单数量-止")
    Integer endOrderNum;


    @ApiModelProperty("注册数量-起")
    Integer startRegisterNum;

    @ApiModelProperty("注册数量-止")
    Integer endRegisterNum;


    @ApiModelProperty("分享次数-起")
    Integer startShareNum;

    @ApiModelProperty("分享次数-止")
    Integer endShareNum;


}
