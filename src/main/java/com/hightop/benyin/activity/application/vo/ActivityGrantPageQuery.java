package com.hightop.benyin.activity.application.vo;


import com.hightop.fario.common.mybatis.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * 活动查询
 *
 * <AUTHOR>
 * @date 2024-01-05 16:28:37
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("活动查询O")
public class ActivityGrantPageQuery extends PageQuery {

    @ApiModelProperty("活动id")
    Long activityId;

    @ApiModelProperty("客户id")
    Long customerId;

    @ApiModelProperty("奖品类型")
    String awardType;

    @ApiModelProperty("客户编号")
    String customerSeqId;

    @ApiModelProperty("客户名称")
    String customerName;


    @ApiModelProperty("奖品名称")
    String awardName;

    @ApiModelProperty("奖品编码")
    String articleCode;


    @ApiModelProperty("状态")
    String status;


}
