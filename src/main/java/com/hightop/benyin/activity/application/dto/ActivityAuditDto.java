package com.hightop.benyin.activity.application.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;

/**
 * @Description: 审核活动DTO
 * @Author: xhg
 * @Date: 2024/1/5 16:00
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ActivityAuditDto {

    @ApiModelProperty("活动id")
    @NotNull(message="活动id不能为空")
    Long id;

    @ApiModelProperty("是否通过")
    @NotNull(message="审核结果不能为空")
    Boolean passed;

}
