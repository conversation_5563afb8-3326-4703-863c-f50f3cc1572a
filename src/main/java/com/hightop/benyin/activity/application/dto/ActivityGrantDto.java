package com.hightop.benyin.activity.application.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description: 活动奖品发放DTO
 * @Author: xhg
 * @Date: 2024/1/5 16:00
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ActivityGrantDto {

    @ApiModelProperty("活动id")
    @NotNull(message = "活动id不能为空")
    Long id;

    @ApiModelProperty("客户id")
    @NotNull(message = "客户id不能为空")
    Long customerId;

    @ApiModelProperty("客户id")
    Long customerAddressId;

    @ApiModelProperty("奖品明细")
    @NotEmpty(message = "奖品明细不能为空")
    List<AwardGrantDto> awardGrants;

}
