package com.hightop.benyin.activity.application.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;

@Data
@ApiModel(value = "活动奖品发放情况vo")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ActivityAwardGrantSummaryVo {

    @ApiModelProperty(value = "发放客户数量")
    Integer customerNum;

    @ApiModelProperty(value = "发放总金额")
    BigDecimal awardAmount;

    @ApiModelProperty(value = "发放耗材数量")
    Integer awardPartNum;

    @ApiModelProperty(value = "发放耗材金额")
    BigDecimal awardPartAmount;

    @ApiModelProperty(value = "发放积分数量")
    Integer awardPointsNum;

    @ApiModelProperty(value = "发放维修数量")
    Integer awardRepairNum;

    @ApiModelProperty(value = "发放代金券数量")
    Integer awardTicketNum;

    @ApiModelProperty(value = "发放代金券金额")
    BigDecimal awardTicketAmount;

    @ApiModelProperty(value = "发放其他数量")
    Integer awardOtherNum;

    @ApiModelProperty(value = "发放其他金额")
    BigDecimal awardOtherAmount;

}
