package com.hightop.benyin.activity.application.vo;


import com.hightop.fario.common.jackson.annotation.JsonAmount;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@Data
@ApiModel(value = "活动情况统计vo")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ActivitySituationSummaryVo {

    @ApiModelProperty("分享次数")
    Integer shareNum = 0;

    @ApiModelProperty("触达客户数")
    Integer clickNum = 0;

    @ApiModelProperty("注册客户数")
    Integer registerNum = 0;

    @ApiModelProperty("消费客户数")
    Integer orderCustNum = 0;

    @ApiModelProperty("消费订单数")
    Integer orderNum = 0;

    @ApiModelProperty("消费金额")
    @JsonAmount
    Long orderAmount = 0L;


}
