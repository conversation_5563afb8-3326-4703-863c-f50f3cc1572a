package com.hightop.benyin.activity.application.vo;


import com.hightop.benyin.activity.infrastructure.enums.AwardType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;

@Data
@ApiModel(value = "活动情况统计vo")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ActivityGrantVo {

    @ApiModelProperty("奖品类型")
    AwardType awardType;

    @ApiModelProperty("奖品名称")
    String awardName;

    @ApiModelProperty("奖品编码")
    String articleCode;

    @ApiModelProperty("总数量")
    Integer totalQuantity;

    @ApiModelProperty("总金额")
    BigDecimal totalAmount;

    @ApiModelProperty("价格")
    BigDecimal price;

}
