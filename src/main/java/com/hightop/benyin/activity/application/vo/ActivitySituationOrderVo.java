package com.hightop.benyin.activity.application.vo;


import com.hightop.benyin.payment.infrastructure.enums.TradeOrderOrigin;
import com.hightop.fario.common.jackson.annotation.JsonAmount;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;

@Data
@ApiModel(value = "活动消费情况vo")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ActivitySituationOrderVo {

    @ApiModelProperty(value = "客户id")
    Long customerId;

    @ApiModelProperty(value = "客户编码")
    String customerSeqId;

    @ApiModelProperty(value = "客户名称")
    String customerName;

    @ApiModelProperty("订单类型")
    TradeOrderOrigin orderOrigin;

    @ApiModelProperty("订单号")
    String orderNum;

    @ApiModelProperty("订单消费金额")
    @JsonAmount
    Long orderAmount=0L;


    @ApiModelProperty("下单人")
    String buyer;

    @ApiModelProperty("下单人电话")
    String buyerPhone;

    @ApiModelProperty("创建时间")
    LocalDateTime createdAt;


}
