package com.hightop.benyin.activity.application.vo;


import com.hightop.fario.common.jackson.annotation.JsonAmount;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;

@Data
@ApiModel(value = "活动情况vo")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ActivitySituationVo {

    @ApiModelProperty(value = "客户id")
    Long customerId;

    @ApiModelProperty(value = "客户编码")
    String customerSeqId;

    @ApiModelProperty(value = "客户名称")
    String customerName;

    @ApiModelProperty("订单数")
    Integer orderNum=0;

    @ApiModelProperty("工单数")
    Integer workOrderNum=0;

    @ApiModelProperty("订单消费金额")
    @JsonAmount
    Long orderAmount=0L;

    @ApiModelProperty("工单消费金额")
    @JsonAmount
    Long workAmount=0L;

    @ApiModelProperty("消费总金额")
    @JsonAmount
    Long totalAmount=0L;

    @ApiModelProperty("登记机器数量")
    Long machineNum=0L;

    @ApiModelProperty("入驻时间")
    LocalDateTime entryTime;

    @ApiModelProperty("省")
    String province;

    @ApiModelProperty("市")
    String city;

    @ApiModelProperty("区")
    String area;

}
