package com.hightop.benyin.activity.domain.service;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.activity.infrastructure.entity.ActivitySku;
import com.hightop.benyin.activity.infrastructure.mapper.ActivitySkuMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/12/28 18:06
 */
@Service
public class ActivitySkuDomainService extends MPJBaseServiceImpl<ActivitySkuMapper, ActivitySku> {

    /**
     * 查询活动商品
     * @param skuIds
     * @return
     */
    public List<ActivitySku> getSkuPromotion( List<Long> skuIds,String consigneeRegionCode){
        return baseMapper.getSkuPromotion(skuIds,consigneeRegionCode);
    }

    /**
     * 查询活动商品
     * @param skuIds
     * @return
     */
    public List<ActivitySku> getSkuActivity( List<Long> skuIds){
        return baseMapper.getSkuActivity(skuIds);
    }
}
