package com.hightop.benyin.activity.domain.service;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.activity.infrastructure.entity.Activity;
import com.hightop.benyin.activity.infrastructure.entity.ActivityAwardGrant;
import com.hightop.benyin.activity.infrastructure.mapper.ActivityAwardGrantMapper;
import com.hightop.benyin.activity.infrastructure.mapper.ActivityMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2023/12/28 18:06
 */
@Service
public class ActivityAwardGrantDomainService extends MPJBaseServiceImpl<ActivityAwardGrantMapper, ActivityAwardGrant> {
}
