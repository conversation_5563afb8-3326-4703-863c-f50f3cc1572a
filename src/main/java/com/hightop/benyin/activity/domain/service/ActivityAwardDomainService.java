package com.hightop.benyin.activity.domain.service;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.activity.infrastructure.entity.ActivityAward;
import com.hightop.benyin.activity.infrastructure.mapper.ActivityAwardMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/12/28 18:06
 */
@Service
public class ActivityAwardDomainService extends MPJBaseServiceImpl<ActivityAwardMapper, ActivityAward> {

    public List<ActivityAward> getAwardList(Long activityId) {
        return baseMapper.getAwardList(activityId);
    }
}
